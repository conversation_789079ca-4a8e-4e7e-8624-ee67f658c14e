/**
 * 智能预加载器 - 基于用户行为预测数据需求
 *
 * 主要特性：
 * 1. 用户行为分析和预测
 * 2. 智能缓存管理
 * 3. 预加载优先级队列
 * 4. 网络状况自适应
 */

interface UserBehavior {
  scrollDirection: 'up' | 'down' | 'none'
  scrollSpeed: number // 像素/秒
  pageChangePattern: 'sequential' | 'random' | 'search'
  averageStayTime: number // 毫秒
  preferredPageSize: number
  lastActions: string[] // 最近的操作历史
}

interface PreloadTask {
  id: string
  priority: number
  dataParams: any
  estimatedSize: number
  createdAt: number
  retryCount: number
}

interface NetworkInfo {
  effectiveType: '2g' | '3g' | '4g' | 'slow-2g' | 'unknown'
  downlink: number // Mbps
  rtt: number // 毫秒
  saveData: boolean
}

interface PreloadConfig {
  maxConcurrentRequests: number
  maxCacheSize: number // MB
  preloadDistance: number // 预加载距离（页数）
  behaviorAnalysisWindow: number // 行为分析窗口（毫秒）
  enableNetworkAdaptation: boolean
  minConfidenceThreshold: number // 最小置信度阈值
}

const DEFAULT_CONFIG: PreloadConfig = {
  maxConcurrentRequests: 3,
  maxCacheSize: 50, // 50MB
  preloadDistance: 2, // 预加载前后2页
  behaviorAnalysisWindow: 30000, // 30秒
  enableNetworkAdaptation: true,
  minConfidenceThreshold: 0.6, // 60%置信度
}

export class SmartPreloader {
  private config: PreloadConfig
  private userBehavior: UserBehavior
  private preloadQueue: PreloadTask[] = []
  private cache = new Map<
    string,
    { data: any; timestamp: number; size: number }
  >()
  private activeRequests = new Set<string>()
  private behaviorHistory: Array<{
    action: string
    timestamp: number
    context: any
  }> = []
  private networkInfo: NetworkInfo | null = null

  constructor(config: Partial<PreloadConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.userBehavior = this.initializeUserBehavior()
    this.initializeNetworkMonitoring()
  }

  /**
   * 记录用户行为
   */
  recordUserAction(action: string, context: any = {}): void {
    const timestamp = Date.now()

    this.behaviorHistory.push({ action, timestamp, context })

    // 保持行为历史在合理范围内
    const cutoffTime = timestamp - this.config.behaviorAnalysisWindow
    this.behaviorHistory = this.behaviorHistory.filter(
      (h) => h.timestamp > cutoffTime
    )

    // 更新用户行为模式
    this.updateUserBehavior(action, context)

    // 基于新行为预测并预加载
    this.predictAndPreload(context)
  }

  /**
   * 预测下一页数据需求
   */
  predictNextDataNeeds(
    currentParams: any
  ): Array<{ params: any; confidence: number }> {
    const predictions: Array<{ params: any; confidence: number }> = []

    // 基于滚动方向预测
    if (this.userBehavior.scrollDirection === 'down') {
      const nextPageParams = {
        ...currentParams,
        offset: (currentParams.offset || 0) + (currentParams.limit || 20),
      }
      predictions.push({ params: nextPageParams, confidence: 0.8 })

      // 如果滚动速度快，预加载更多页
      if (this.userBehavior.scrollSpeed > 1000) {
        const nextNextPageParams = {
          ...currentParams,
          offset: (currentParams.offset || 0) + (currentParams.limit || 20) * 2,
        }
        predictions.push({ params: nextNextPageParams, confidence: 0.6 })
      }
    } else if (this.userBehavior.scrollDirection === 'up') {
      const prevPageParams = {
        ...currentParams,
        offset: Math.max(
          0,
          (currentParams.offset || 0) - (currentParams.limit || 20)
        ),
      }
      predictions.push({ params: prevPageParams, confidence: 0.7 })
    }

    // 基于页面变化模式预测
    if (this.userBehavior.pageChangePattern === 'sequential') {
      // 顺序浏览模式，预加载相邻页面
      for (let i = 1; i <= this.config.preloadDistance; i++) {
        const nextParams = {
          ...currentParams,
          offset: (currentParams.offset || 0) + (currentParams.limit || 20) * i,
        }
        const confidence = Math.max(0.3, 0.8 - i * 0.2)
        predictions.push({ params: nextParams, confidence })
      }
    }

    // 过滤低置信度预测
    return predictions.filter(
      (p) => p.confidence >= this.config.minConfidenceThreshold
    )
  }

  /**
   * 执行预加载
   */
  async preload(
    dataFetcher: (params: any) => Promise<any>,
    currentParams: any
  ): Promise<void> {
    const predictions = this.predictNextDataNeeds(currentParams)

    for (const prediction of predictions) {
      const cacheKey = this.generateCacheKey(prediction.params)

      // 检查是否已缓存或正在加载
      if (this.cache.has(cacheKey) || this.activeRequests.has(cacheKey)) {
        continue
      }

      // 检查网络状况
      if (
        this.config.enableNetworkAdaptation &&
        !this.shouldPreloadOnCurrentNetwork()
      ) {
        continue
      }

      // 添加到预加载队列
      this.addPreloadTask({
        id: cacheKey,
        priority: Math.floor(prediction.confidence * 100),
        dataParams: prediction.params,
        estimatedSize: this.estimateDataSize(prediction.params),
        createdAt: Date.now(),
        retryCount: 0,
      })
    }

    // 处理预加载队列
    this.processPreloadQueue(dataFetcher)
  }

  /**
   * 获取缓存数据
   */
  getCachedData(params: any): any | null {
    const cacheKey = this.generateCacheKey(params)
    const cached = this.cache.get(cacheKey)

    if (cached) {
      // 检查是否过期（5分钟）
      if (Date.now() - cached.timestamp < 5 * 60 * 1000) {
        return cached.data
      } else {
        this.cache.delete(cacheKey)
      }
    }

    return null
  }

  /**
   * 清理缓存
   */
  cleanupCache(): void {
    const now = Date.now()
    const maxAge = 10 * 60 * 1000 // 10分钟
    let totalSize = 0

    // 移除过期数据
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > maxAge) {
        this.cache.delete(key)
      } else {
        totalSize += value.size
      }
    }

    // 如果缓存过大，移除最旧的数据
    if (totalSize > this.config.maxCacheSize * 1024 * 1024) {
      const entries = Array.from(this.cache.entries()).sort(
        ([, a], [, b]) => a.timestamp - b.timestamp
      )

      for (const [key] of entries) {
        this.cache.delete(key)
        totalSize -= this.cache.get(key)?.size || 0
        if (totalSize <= this.config.maxCacheSize * 1024 * 1024 * 0.8) {
          break
        }
      }
    }
  }

  /**
   * 获取预加载统计信息
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      cacheMemoryUsage: Array.from(this.cache.values()).reduce(
        (sum, item) => sum + item.size,
        0
      ),
      queueLength: this.preloadQueue.length,
      activeRequests: this.activeRequests.size,
      userBehavior: { ...this.userBehavior },
      networkInfo: this.networkInfo,
      behaviorHistoryLength: this.behaviorHistory.length,
    }
  }

  // 私有方法

  private initializeUserBehavior(): UserBehavior {
    return {
      scrollDirection: 'none',
      scrollSpeed: 0,
      pageChangePattern: 'sequential',
      averageStayTime: 5000,
      preferredPageSize: 20,
      lastActions: [],
    }
  }

  private initializeNetworkMonitoring(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      this.networkInfo = {
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        saveData: connection.saveData || false,
      }

      connection.addEventListener('change', () => {
        this.networkInfo = {
          effectiveType: connection.effectiveType || 'unknown',
          downlink: connection.downlink || 0,
          rtt: connection.rtt || 0,
          saveData: connection.saveData || false,
        }
      })
    }
  }

  private updateUserBehavior(action: string, context: any): void {
    this.userBehavior.lastActions.push(action)
    if (this.userBehavior.lastActions.length > 10) {
      this.userBehavior.lastActions.shift()
    }

    // 分析滚动行为
    if (action === 'scroll') {
      this.userBehavior.scrollDirection = context.direction || 'none'
      this.userBehavior.scrollSpeed = context.speed || 0
    }

    // 分析页面变化模式
    if (action === 'page-change') {
      const recentPageChanges = this.behaviorHistory
        .filter((h) => h.action === 'page-change')
        .slice(-5)

      if (recentPageChanges.length >= 3) {
        const isSequential = recentPageChanges.every((change, index) => {
          if (index === 0) return true
          const prev = recentPageChanges[index - 1]
          const currentOffset = change.context.offset || 0
          const prevOffset = prev.context.offset || 0
          const pageSize = change.context.limit || 20
          return Math.abs(currentOffset - prevOffset) === pageSize
        })

        this.userBehavior.pageChangePattern = isSequential
          ? 'sequential'
          : 'random'
      }
    }
  }

  private predictAndPreload(context: any): void {
    // 这里可以添加更复杂的预测逻辑
    // 目前简化为基于当前上下文的基本预测
  }

  private shouldPreloadOnCurrentNetwork(): boolean {
    if (!this.networkInfo) return true

    // 在慢速网络或省流量模式下限制预加载
    if (this.networkInfo.saveData) return false
    if (
      this.networkInfo.effectiveType === 'slow-2g' ||
      this.networkInfo.effectiveType === '2g'
    ) {
      return false
    }

    return true
  }

  private addPreloadTask(task: PreloadTask): void {
    this.preloadQueue.push(task)
    this.preloadQueue.sort((a, b) => b.priority - a.priority)
  }

  private async processPreloadQueue(
    dataFetcher: (params: any) => Promise<any>
  ): Promise<void> {
    while (
      this.preloadQueue.length > 0 &&
      this.activeRequests.size < this.config.maxConcurrentRequests
    ) {
      const task = this.preloadQueue.shift()!
      this.executePreloadTask(task, dataFetcher)
    }
  }

  private async executePreloadTask(
    task: PreloadTask,
    dataFetcher: (params: any) => Promise<any>
  ): Promise<void> {
    this.activeRequests.add(task.id)

    try {
      const data = await dataFetcher(task.dataParams)

      // 缓存数据
      this.cache.set(task.id, {
        data,
        timestamp: Date.now(),
        size: task.estimatedSize,
      })

      if (import.meta.env.DEV) {
        console.log(`[SmartPreloader] 预加载完成: ${task.id}`)
      }
    } catch (error) {
      console.warn(`[SmartPreloader] 预加载失败: ${task.id}`, error)

      // 重试逻辑
      if (task.retryCount < 2) {
        task.retryCount++
        task.priority = Math.max(1, task.priority - 10)
        this.addPreloadTask(task)
      }
    } finally {
      this.activeRequests.delete(task.id)
    }
  }

  private generateCacheKey(params: any): string {
    return JSON.stringify(params, Object.keys(params).sort())
  }

  private estimateDataSize(params: any): number {
    const pageSize = params.limit || 20
    const estimatedRowSize = 1024 // 1KB per row
    return pageSize * estimatedRowSize
  }
}

// 全局智能预加载器实例
export const globalSmartPreloader = new SmartPreloader()

/**
 * 智能预加载 Hook
 */
export function useSmartPreloader() {
  const preloader = globalSmartPreloader

  /**
   * 记录用户滚动行为
   */
  const recordScrollBehavior = (direction: 'up' | 'down', speed: number) => {
    preloader.recordUserAction('scroll', { direction, speed })
  }

  /**
   * 记录页面变化
   */
  const recordPageChange = (params: any) => {
    preloader.recordUserAction('page-change', params)
  }

  /**
   * 记录搜索行为
   */
  const recordSearchBehavior = (query: string, filters: any) => {
    preloader.recordUserAction('search', { query, filters })
  }

  /**
   * 执行智能预加载
   */
  const executePreload = async (
    dataFetcher: (params: any) => Promise<any>,
    currentParams: any
  ) => {
    await preloader.preload(dataFetcher, currentParams)
  }

  /**
   * 获取缓存数据
   */
  const getCachedData = (params: any) => {
    return preloader.getCachedData(params)
  }

  /**
   * 清理缓存
   */
  const cleanupCache = () => {
    preloader.cleanupCache()
  }

  /**
   * 获取统计信息
   */
  const getStats = () => {
    return preloader.getStats()
  }

  return {
    recordScrollBehavior,
    recordPageChange,
    recordSearchBehavior,
    executePreload,
    getCachedData,
    cleanupCache,
    getStats,
  }
}

// 在开发环境暴露调试接口
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__SMART_PRELOADER__ = {
    preloader: globalSmartPreloader,
    stats: () => console.table(globalSmartPreloader.getStats()),
    cleanup: () => globalSmartPreloader.cleanupCache(),
  }
}
