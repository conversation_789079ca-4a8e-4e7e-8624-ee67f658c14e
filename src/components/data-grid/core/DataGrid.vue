<template>
  <div
    class="flex flex-col overflow-hidden relative"
    :style="containerStyle"
    ref="containerRef"
  >
    <DGToolbar
      v-if="toolbarOptions"
      v-bind="toolbarOptions"
      ref="toolbarRef"
      class="flex-none"
      v-memo="[
        JSON.stringify(toolbarOptions?.title),
        typeof toolbarOptions?.total === 'object' && toolbarOptions?.total
          ? (toolbarOptions.total as any).value
          : toolbarOptions?.total,
        toolbarOptions?.queryParams?.offset,
        toolbarOptions?.queryParams?.limit,
      ]"
    />
    <div class="flex-1 min-h-0 relative" ref="gridContainerRef">
      <vxe-grid
        v-bind="processedGridOptionsWithoutToolbar"
        height="100%"
        ref="gridRef"
        v-on="gridEvents"
        v-memo="vMemoStrategy.deps"
      >
        <template
          v-for="(slotInfo, slotName) in customSlots"
          :key="`slot-${slotName}-${componentId}`"
          #[slotName]="params"
        >
          <slot :name="slotName" v-bind="params">
            <SmartCellRenderer
              v-if="
                performanceStatus.performanceLevel === 'excellent' ||
                performanceStatus.performanceLevel === 'good'
              "
              :slot-info="slotInfo"
              :params="params"
              :is-destroying="isDestroying"
              :render-slot-with-config="renderSlotWithConfig"
              :get-component-props="getComponentProps"
            />
            <CellRenderer
              v-else
              :slot-info="slotInfo"
              :params="params"
              :is-destroying="isDestroying"
              :render-slot-with-config="renderSlotWithConfig"
              :get-component-props="getComponentProps"
            />
          </slot>
        </template>
      </vxe-grid>

      <DataGridLoading :visible="isLoading" :config="loadingConfig" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  provide,
  shallowRef,
  onBeforeUnmount,
  markRaw,
} from 'vue'
import { useResizeObserver, useWindowSize } from '@vueuse/core'
import type { DataGridProps } from '../types'
import type { UseDataGridReturn } from '../types'
import DGToolbar from './DGToolbar.vue'
import DataGridLoading from './DataGridLoading.vue'
import CellRenderer from './CellRenderer.vue'
import SmartCellRenderer from './SmartCellRenderer.vue'
import { getPluginManager } from '../plugins'
import { performanceMonitor } from '../utils/performanceMonitor'
import { buildStatusMapFromMetadata } from '../utils/columnHelpers'
import { getDynamicVirtualScrollThreshold } from '../config/performance'
// import { useIncrementalRenderer } from './IncrementalRenderer'

// DataGrid 组件属性定义
interface DataGridPropsWithInstance extends DataGridProps {
  // 数据网格实例，通过 useDataGrid 创建
  dataGridInstance?: UseDataGridReturn
}
const props = withDefaults(defineProps<DataGridPropsWithInstance>(), {
  moduleModel: undefined,
  gridOptions: () => ({}),
  dataGridInstance: undefined,
})

// 提供实例给子组件 & 初始化插件系统
provide('dataGridInstance', props.dataGridInstance)
const pluginManager = getPluginManager()

// 优化组件缓存
const cachedComponentTypes = shallowRef(new Map<string, unknown>())
const isDestroying = ref(false)

const initializeComponents = () => {
  // 直接从插件管理器获取所有已注册的组件
  const registeredComponents = pluginManager.getAllComponents()

  // 复制到缓存中
  const components = new Map(registeredComponents)

  // 添加默认组件
  const defaultComponent = markRaw({ render: () => null })
  components.set('__fallback__', defaultComponent)
  components.set('__empty__', defaultComponent)

  cachedComponentTypes.value = components

  // 开发环境下打印组件信息
  if (import.meta.env.DEV) {
    console.log(
      '🔧 [DataGrid] 已缓存组件:',
      Array.from(components.keys()).filter((name) => !name.startsWith('__'))
    )
  }
}

onMounted(initializeComponents)

// 性能监控
const componentId = computed(
  () => props.dataGridInstance?.moduleModel || props.moduleModel || 'unknown'
)

if (import.meta.env.DEV) {
  onMounted(() => {
    performanceMonitor.startComponentMonitoring(componentId.value)

    // 输出性能建议
    nextTick(() => {
      const status = performanceStatus.value
      console.group(`📊 [DataGrid ${componentId.value}] 性能状态`)
      console.log(`总单元格数: ${status.totalCells}`)
      console.log(`虚拟滚动阈值: ${status.threshold}`)
      console.log(`性能等级: ${status.performanceLevel}`)

      if (status.shouldUseVirtualScroll) {
        console.warn(
          `💡 建议启用虚拟滚动 (当前 ${dataLength.value} 行 > ${status.threshold} 行阈值)`
        )
      }

      if (status.performanceLevel === 'warning') {
        console.warn('⚠️ 性能警告: 数据量较大，建议优化')
      } else if (status.performanceLevel === 'critical') {
        console.error('🚨 性能严重: 数据量过大，强烈建议优化')
      }

      console.groupEnd()
    })
  })
}

const gridRef = ref()
const toolbarRef = ref()
const containerRef = ref()

const gridContainerRef = ref()

// 响应式高度状态 - 使用VueUse的useWindowSize替代手动监听
const { height: viewportHeight } = useWindowSize()

// 这些计算属性将在 processedGridOptionsWithoutToolbar 定义后移动

// 这些计算属性将在 processedGridOptionsWithoutToolbar 定义后移动

// 这些计算属性将在 processedGridOptionsWithoutToolbar 定义后移动

// 智能获取有效的 gridOptions
const effectiveGridOptions = computed(() => {
  if (props.gridOptions && Object.keys(props.gridOptions).length > 0) {
    return props.gridOptions
  }

  if (props.dataGridInstance?.gridOptions?.value) {
    return props.dataGridInstance.gridOptions.value
  }

  return {}
})

// 提取配置
const toolbarOptions = computed(() => effectiveGridOptions.value.toolbarOptions)
const isLoading = computed(() => effectiveGridOptions.value.loading || false)
const loadingConfig = computed(() => ({
  type: 'spinner' as const,
  text: '数据加载中...',
  size: 'medium' as const,
  color: 'primary' as const,
  theme: 'auto' as const,
  ...effectiveGridOptions.value.loadingConfig,
}))

// 计算自动高度
const calculateAutoHeight = () => {
  if (!containerRef.value) return 600
  const rect = containerRef.value.getBoundingClientRect()
  const availableHeight = viewportHeight.value - rect.top - 20
  return Math.max(availableHeight, 300)
}

// 计算容器样式
const containerStyle = computed(() => {
  const height = effectiveGridOptions.value.height

  if (height && height !== 'auto') {
    return {
      height:
        typeof height === 'number' || !isNaN(Number(height))
          ? `${height}px`
          : height,
    }
  }

  return { height: `${calculateAutoHeight()}px` }
})

// 使用ResizeObserver监听容器尺寸变化，触发重新计算
useResizeObserver(containerRef, () => {
  nextTick(() => {
    if (!isDestroying.value && containerRef.value && gridRef.value) {
      // 当容器尺寸变化时，通知vxe-grid重新计算布局
      try {
        gridRef.value.refreshColumn()
      } catch (error) {
        if (import.meta.env.DEV) {
          console.warn('[DataGrid] refreshColumn 出错:', error)
        }
      }
    }
  })
})

const gridEvents = computed(() => effectiveGridOptions.value.gridEvents || {})

// 简化插槽名称生成
const generateSlotName = (col: unknown, slotType: string): string => {
  const field = (col as any)?.field || (col as any)?.type || 'col'
  return `${field}_${slotType}`
}

// 处理列配置，将插槽配置转换为 vxe-table 格式
const processedGridOptions = computed(() => {
  const options = { ...effectiveGridOptions.value }
  if (!options.columns) return options

  const processColumns = (cols: unknown[]): unknown[] => {
    return cols.map((col) => {
      const colObj = col as Record<string, unknown>
      const processedCol = { ...colObj }

      if (colObj.slots) {
        const vxeSlots: Record<string, string> = {}
        const slotObj = colObj.slots as Record<string, unknown>
        Object.keys(slotObj).forEach((slotType) => {
          const slotConfig = slotObj[slotType]
          if (slotConfig) {
            vxeSlots[slotType] =
              typeof slotConfig === 'string'
                ? slotConfig
                : generateSlotName(col, slotType)
          }
        })
        processedCol.slots = vxeSlots
      }

      if (colObj.children && Array.isArray(colObj.children)) {
        processedCol.children = processColumns(colObj.children)
      }

      return processedCol
    })
  }

  options.columns = processColumns(options.columns)
  return options
})

// 处理后的网格选项（移除工具栏）
const processedGridOptionsWithoutToolbar = computed(() => {
  const { toolbarOptions, height, loading, loadingConfig, ...gridOptions } =
    processedGridOptions.value
  return {
    ...gridOptions,
    autoResize: false,
    syncResize: true,
    keepSource: false,
    loading: false,
  }
})

// 优化后的 v-memo 依赖项 - 缓存列长度和数据长度以提高性能
const columnsLength = computed(
  () => processedGridOptionsWithoutToolbar.value.columns?.length || 0
)
const dataLength = computed(
  () => processedGridOptionsWithoutToolbar.value.data?.length || 0
)

// 🚀 性能优化：分层级 v-memo 策略
const vMemoStrategy = computed(() => {
  const totalCells = columnsLength.value * dataLength.value

  if (totalCells <= 500) {
    return {
      enabled: true,
      level: 'full',
      deps: [
        columnsLength.value,
        dataLength.value,
        processedGridOptionsWithoutToolbar.value.loading,
      ],
    }
  } else if (totalCells <= 2000) {
    return {
      enabled: true,
      level: 'partial',
      deps: [columnsLength.value, dataLength.value],
    }
  } else {
    return {
      enabled: false,
      level: 'disabled',
      deps: [],
    }
  }
})

// 向后兼容的 shouldUseVMemo（保留以备将来使用）
// const shouldUseVMemo = computed(() => vMemoStrategy.value.enabled)

// 🚀 动态虚拟滚动阈值
const dynamicVirtualScrollThreshold = computed(() => {
  return getDynamicVirtualScrollThreshold(columnsLength.value, dataLength.value)
})

// 增量渲染器（保留以备将来使用）
// const incrementalRenderer = useIncrementalRenderer()

// 性能监控和建议
const performanceStatus = computed(() => {
  const totalCells = columnsLength.value * dataLength.value
  const threshold = dynamicVirtualScrollThreshold.value

  return {
    totalCells,
    threshold,
    shouldUseVirtualScroll: dataLength.value > threshold,
    shouldUseIncrementalRender: totalCells > 2000, // 超过2000个单元格启用增量渲染
    performanceLevel:
      totalCells <= 1000
        ? 'excellent'
        : totalCells <= 5000
          ? 'good'
          : totalCells <= 10000
            ? 'warning'
            : 'critical',
  }
})

// 监听数据变化，在大数据量时使用增量渲染
watch(
  () => [dataLength.value, columnsLength.value],
  ([newDataLength, newColumnsLength]) => {
    const totalCells = newDataLength * newColumnsLength

    if (totalCells > 2000 && import.meta.env.DEV) {
      console.log(
        `🚀 [DataGrid] 检测到大数据量 (${totalCells} 单元格)，建议启用增量渲染`
      )

      // 在开发环境中提供增量渲染的使用示例
      console.group('💡 增量渲染使用建议')
      console.log('1. 考虑分批加载数据')
      console.log('2. 优先渲染可见区域')
      console.log('3. 使用虚拟滚动减少DOM节点')
      console.log('4. 启用组件实例池复用')
      console.groupEnd()
    }
  },
  { immediate: true }
)

// generateSlotName 已移动到更早的位置

onBeforeUnmount(() => {
  isDestroying.value = true

  // 清理性能监控器
  if (import.meta.env.DEV) {
    try {
      performanceMonitor.stopMonitoring(componentId.value)
    } catch (error) {
      console.warn('[DataGrid] 清理性能监控器时出错:', error)
    }
  }

  // 安全清理缓存
  try {
    if (cachedComponentTypes.value) {
      cachedComponentTypes.value.clear()
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('[DataGrid] 清理组件缓存时出错:', error)
    }
  }
})

const processColumnsRecursively = (
  cols: unknown[],
  processor: (col: unknown) => void
) => {
  cols.forEach((col) => {
    processor(col)
    const colObj = col as Record<string, unknown>
    if (colObj.children && Array.isArray(colObj.children)) {
      processColumnsRecursively(colObj.children, processor)
    }
  })
}

// 插槽配置类型定义
interface SlotConfig {
  component?: any
  props?: Record<string, unknown>
  render?: Function
  slots?: Record<string, unknown>
  [key: string]: unknown
}

// 简化插槽映射提取
const customSlots = computed(() => {
  const columns = effectiveGridOptions.value.columns || []
  if (!Array.isArray(columns) || columns.length === 0) return {}

  const slots: Record<
    string,
    {
      slotName: string
      config: SlotConfig
      field: string
      slotType: string
      column: unknown
    }
  > = {}

  processColumnsRecursively(columns, (col) => {
    const colObj = col as Record<string, unknown>
    if (colObj.slots) {
      const slotObj = colObj.slots as Record<string, unknown>
      Object.keys(slotObj).forEach((slotType) => {
        const slotConfig = slotObj[slotType]
        if (slotConfig && typeof slotConfig === 'object') {
          const slotName = generateSlotName(col, slotType)
          slots[slotName] = {
            slotName,
            config: slotConfig as SlotConfig,
            field: (colObj.field as string) || (colObj.type as string),
            slotType,
            column: col,
          }
        }
      })
    }
  })

  return slots
})

// processedGridOptions 已移动到更早的位置

// 使用插槽配置渲染内容
const renderSlotWithConfig = (
  slotInfo: {
    slotName: string
    config: SlotConfig
    field: string
    slotType: string
  },
  params: Record<string, unknown>
) => {
  const { config, field } = slotInfo
  if (!config) return params.value || ''

  const configObj = config as Record<string, unknown>
  const renderParams = {
    ...params,
    value: (params.row as Record<string, unknown>)?.[field] || params.value,
    field,
  }

  try {
    // ColumnHelper 生成的配置：slots.default.render
    const slotsConfig = configObj.slots as Record<string, unknown>
    const defaultSlot = slotsConfig?.default as Record<string, unknown>
    if (defaultSlot?.render && typeof defaultSlot.render === 'function') {
      return (
        (defaultSlot.render as Function)(renderParams) || params.value || ''
      )
    }

    // 向后兼容：直接 render 函数
    if (typeof configObj.render === 'function') {
      return (configObj.render as Function)(renderParams) || params.value || ''
    }

    return params.value || ''
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error(`[DataGrid] 插槽渲染失败 (${slotInfo.slotName}):`, error)
    }
    return params.value || ''
  }
}

const getComponentProps = (
  slotInfo: {
    config: SlotConfig
    field: string
    column: unknown
  },
  params: Record<string, unknown>
) => {
  const { config, field, column } = slotInfo
  const baseProps = config?.props || {}

  const enhancedProps: Record<string, unknown> = {
    ...baseProps,
    column, // 传递完整的列配置
    field, // 传递字段名
    row: params.row, // 传递行数据
    value: (params.row as Record<string, unknown>)?.[field] ?? params.value, // 传递当前值
  }

  // 特殊处理 StatusRenderer：预处理 statusMap
  // 注意：这里的组件识别方式需要根据实际情况调整，例如通过组件名或唯一标识
  if (
    config?.component?.name === 'StatusRenderer' &&
    (baseProps as any).config?.autoFromMetadata
  ) {
    const columnObj = column as Record<string, unknown>
    const processedStatusMap = buildStatusMapFromMetadata(
      columnObj?.enumInfo as
        | { enum_values: Record<string, string> }
        | undefined,
      (baseProps as any).config?.statusMap
    )
    enhancedProps.config = {
      ...(baseProps as any).config,
      statusMap: processedStatusMap,
      autoFromMetadata: false, // 已经处理过了，避免重复处理
    }
  }

  return enhancedProps
}

onMounted(() => {
  if (props.dataGridInstance && gridRef.value) {
    props.dataGridInstance._setGridRef(gridRef.value)
  }
})

// 重复的 onBeforeUnmount 已合并到上面

onUnmounted(() => {
  // 清理组件缓存，防止内存泄漏
  try {
    if (cachedComponentTypes.value) {
      cachedComponentTypes.value.clear()
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('[DataGrid] 清理组件缓存时出错:', error)
    }
  }
})

watch(gridRef, (newGridRef) => {
  if (!isDestroying.value && props.dataGridInstance && newGridRef) {
    props.dataGridInstance._setGridRef(newGridRef)
  }
})

// 监听数据变化并重新加载数据
watch(
  () => effectiveGridOptions.value.data,
  (newData, oldData) => {
    if (!isDestroying.value && newData !== oldData) {
      // 如果gridRef可用且有新数据，强制重新加载
      if (gridRef.value && newData) {
        try {
          gridRef.value.reloadData(newData)
        } catch (error) {
          if (import.meta.env.DEV) {
            console.warn('[DataGrid] reloadData 出错:', error)
          }
        }
      }
    }
  },
  { deep: false }
)

// 专门监听列配置变化，需要重新初始化组件
watch(
  () => props.columns,
  () => {
    if (!isDestroying.value) {
      // 列配置变化时，清理组件缓存并重新初始化
      if (cachedComponentTypes.value) {
        cachedComponentTypes.value.clear()
      }
      nextTick(() => initializeComponents())
    }
  },
  { deep: false }
)

// 暴露 vxe-grid 实例的方法给父组件
defineExpose({
  // 直接暴露 vxe-grid 实例
  getGridInstance: () => gridRef.value,
  // 暴露 ModelApi 实例
  getModelApi: () => props.dataGridInstance?.modelApi.value,
  // 暴露常用的 vxe-grid 方法
  getCheckboxRecords: () => gridRef.value?.getCheckboxRecords(),
  getRadioRecord: () => gridRef.value?.getRadioRecord(),
  getCurrentRecord: () => gridRef.value?.getCurrentRecord(),
  setCheckboxRow: (rows: unknown, checked: boolean) =>
    gridRef.value?.setCheckboxRow(rows, checked),
  setAllCheckboxRow: (checked: boolean) =>
    gridRef.value?.setAllCheckboxRow(checked),
  clearCheckboxRow: () => gridRef.value?.clearCheckboxRow(),
  clearCurrentRow: () => gridRef.value?.clearCurrentRow(),
  // 修复刷新方法，使用正确的 vxe-grid 方法
  refreshData: () => {
    if (props.dataGridInstance && props.dataGridInstance.refreshData) {
      // 调用 useDataGrid 中的 refreshData 方法
      props.dataGridInstance.refreshData()
    } else if (gridRef.value) {
      // 备用方案：使用 vxe-grid 的方法
      if (typeof gridRef.value.commitData === 'function') {
        gridRef.value.commitData()
      }
      // 或者重新设置数据来触发刷新
      const currentData = gridRef.value.getData()
      if (currentData) {
        gridRef.value.setData(currentData)
      }
    }
  },
  // 添加新的统一选择方法
  getSelection: () => props.dataGridInstance?.getSelection() || [],
  clearSelection: () => props.dataGridInstance?.clearSelection(),
  setSelection: (
    rows: unknown | unknown[],
    checked?: boolean,
    selectionType?: 'checkbox' | 'radio' | 'seq'
  ) => props.dataGridInstance?.setSelection(rows, checked, selectionType),
  setAllSelection: (checked?: boolean) =>
    props.dataGridInstance?.setAllSelection(checked),
  getSelectionCount: () => props.dataGridInstance?.getSelectionCount() || 0,
  hasSelection: () => props.dataGridInstance?.hasSelection() || false,
  // 可以根据需要添加更多方法
})
</script>

<style scoped>
/* DataGrid Footer 统计行样式 */
:deep(.vxe-table .vxe-table--footer-wrapper) {
  .vxe-footer--row {
    background-color: #fafafa;
    border-top: 2px solid #e8e8e8;
    font-weight: 500;

    .vxe-footer--column {
      color: #333;

      &:first-child {
        font-weight: bold;
      }
    }
  }
}

/* 支持自定义背景色 */
:deep(.vxe-table .vxe-table--footer-wrapper .vxe-footer--row[data-custom-bg]) {
  background-color: var(--footer-bg-color, #fafafa);
}

/* 支持固定 footer */
:deep(.vxe-table.is--footer-sticky .vxe-table--footer-wrapper) {
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 响应式样式 */
@media (max-width: 768px) {
  :deep(.vxe-table .vxe-table--footer-wrapper) {
    .vxe-footer--row {
      font-size: 12px;
    }
  }
}

/* CompositeRenderer 自动行高支持 */
:deep(.vxe-table .vxe-body--row) {
  .vxe-body--column {
    /* 允许单元格内容自动撑开高度 */
    height: auto !important;
    min-height: 30px;

    .vxe-cell {
      /* 移除固定高度限制 */
      height: auto !important;
      min-height: inherit;
      padding: 2px 2px;

      /* 支持多行内容 */
      white-space: normal;
      word-wrap: break-word;
      overflow: visible;
    }
  }

  /* 确保行高度自适应 */
  &.has-composite-renderer {
    height: auto !important;
    min-height: 30px;
  }
}
</style>
