<template>
  <div ref="cellElement" class="smart-cell-renderer">
    <component
      v-if="shouldRenderComponent"
      :is="componentInstance"
      v-bind="componentProps"
      :key="componentKey"
      @vue:unmounted="handleComponentUnmount"
    />
    <div v-else-if="shouldRenderHtml" v-html="htmlContent" />
    <span v-else>{{ fallbackContent }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, ref, watch, onMounted, nextTick } from 'vue'
import { globalAdvancedComponentPool } from '../plugins/AdvancedComponentPool'
import { useRenderPriorityQueue } from './RenderPriorityQueue'

interface Props {
  slotInfo: {
    config?: {
      component?: any
    }
  }
  params: {
    rowIndex?: number
    [key: string]: any
  }
  isDestroying: boolean
  renderSlotWithConfig?: (slotInfo: any, params: any) => string
  getComponentProps?: (slotInfo: any, params: any) => Record<string, any>
}

const props = defineProps<Props>()

// 当前使用的组件实例
const currentComponentInstance = ref<any>(null)
const componentName = ref<string>('')

// 渲染优先级队列
const renderQueue = useRenderPriorityQueue()
const cellElement = ref<HTMLElement | null>(null)

// 计算是否应该渲染组件
const shouldRenderComponent = computed(() => {
  return (
    !props.isDestroying &&
    props.slotInfo.config?.component &&
    props.getComponentProps
  )
})

// 计算是否应该渲染 HTML
const shouldRenderHtml = computed(() => {
  return (
    !props.isDestroying &&
    !props.slotInfo.config?.component &&
    props.slotInfo &&
    props.params &&
    props.renderSlotWithConfig
  )
})

// 智能获取组件实例
const componentInstance = computed(() => {
  if (!shouldRenderComponent.value) {
    return null
  }

  const component = props.slotInfo.config?.component
  if (!component) {
    return null
  }

  // 生成组件名称
  const newComponentName =
    component.name || component.__name || 'UnknownComponent'

  // 如果组件类型发生变化，释放旧实例并获取新实例
  if (componentName.value !== newComponentName) {
    if (currentComponentInstance.value && componentName.value) {
      globalAdvancedComponentPool.release(
        componentName.value,
        currentComponentInstance.value
      )
    }

    componentName.value = newComponentName
    currentComponentInstance.value = globalAdvancedComponentPool.acquire(
      newComponentName,
      () => component
    )
  }

  return currentComponentInstance.value
})

// 计算组件属性
const componentProps = computed(() => {
  if (!shouldRenderComponent.value || !props.getComponentProps) {
    return {}
  }
  return props.getComponentProps(props.slotInfo, props.params)
})

// 计算组件键，用于强制重新渲染
const componentKey = computed(() => {
  return `smart-cell-${props.params.rowIndex || 0}-${componentName.value}`
})

// 计算 HTML 内容
const htmlContent = computed(() => {
  if (!shouldRenderHtml.value || !props.renderSlotWithConfig) {
    return ''
  }
  return props.renderSlotWithConfig(props.slotInfo, props.params)
})

// 计算回退内容
const fallbackContent = computed(() => {
  if (props.isDestroying) return ''
  return props.params?.value || '-'
})

// 处理组件卸载
const handleComponentUnmount = () => {
  // 组件卸载时不立即释放，让池管理器决定何时清理
}

// 监听销毁状态
watch(
  () => props.isDestroying,
  (isDestroying) => {
    if (isDestroying && currentComponentInstance.value && componentName.value) {
      globalAdvancedComponentPool.release(
        componentName.value,
        currentComponentInstance.value
      )
      currentComponentInstance.value = null
      componentName.value = ''
    }
  }
)

// 组件挂载时注册渲染任务
onMounted(() => {
  nextTick(() => {
    if (cellElement.value) {
      // 根据组件类型确定渲染优先级
      const priority = shouldRenderComponent.value ? 'high' : 'normal'
      const taskId = `cell-${componentKey.value}`

      if (priority === 'high') {
        renderQueue.addCriticalTask(taskId, cellElement.value, async () => {
          // 高优先级组件的渲染逻辑
          if (shouldRenderComponent.value) {
            await nextTick()
          }
        })
      } else {
        renderQueue.addNormalTask(taskId, cellElement.value, async () => {
          // 普通优先级的渲染逻辑
          await nextTick()
        })
      }
    }
  })
})

// 组件卸载时清理
onBeforeUnmount(() => {
  if (currentComponentInstance.value && componentName.value) {
    globalAdvancedComponentPool.release(
      componentName.value,
      currentComponentInstance.value
    )
  }
})
</script>

<style scoped>
/* 优化渲染性能的样式 */
.smart-cell-renderer {
  contain: layout style paint;
  will-change: auto;
}

/* 减少重绘的样式 */
.smart-cell-renderer * {
  backface-visibility: hidden;
  transform: translateZ(0);
}
</style>
