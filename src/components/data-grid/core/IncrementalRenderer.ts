/**
 * 增量渲染器 - 时间切片渲染大数据集
 *
 * 主要特性：
 * 1. 时间切片渲染，避免长任务阻塞
 * 2. 优先级队列，优先渲染可见区域
 * 3. 自适应批次大小
 * 4. 渲染进度监控
 */

interface RenderTask {
  id: string
  priority: 'critical' | 'high' | 'normal' | 'low'
  data: any
  renderFn: (data: any) => Promise<void> | void
  estimatedTime: number // 预估渲染时间（毫秒）
}

interface RenderBatch {
  tasks: RenderTask[]
  totalEstimatedTime: number
  startTime: number
}

interface IncrementalRenderConfig {
  maxFrameTime: number // 每帧最大渲染时间（毫秒）
  batchSize: number // 初始批次大小
  maxBatchSize: number // 最大批次大小
  minBatchSize: number // 最小批次大小
  priorityWeights: Record<string, number> // 优先级权重
  enableAdaptiveBatching: boolean // 启用自适应批次大小
}

const DEFAULT_CONFIG: IncrementalRenderConfig = {
  maxFrameTime: 16, // 60fps 下每帧约16ms
  batchSize: 10,
  maxBatchSize: 50,
  minBatchSize: 5,
  priorityWeights: {
    critical: 1000,
    high: 100,
    normal: 10,
    low: 1,
  },
  enableAdaptiveBatching: true,
}

export class IncrementalRenderer {
  private config: IncrementalRenderConfig
  private taskQueue: RenderTask[] = []
  private isRendering = false
  private renderStats = {
    totalTasks: 0,
    completedTasks: 0,
    averageTaskTime: 16,
    adaptiveBatchSize: 10,
  }
  private abortController: AbortController | null = null

  constructor(config: Partial<IncrementalRenderConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.renderStats.adaptiveBatchSize = this.config.batchSize
  }

  /**
   * 添加渲染任务
   */
  addTask(task: Omit<RenderTask, 'estimatedTime'>): void {
    const estimatedTime = this.estimateTaskTime(task)
    const fullTask: RenderTask = { ...task, estimatedTime }

    this.taskQueue.push(fullTask)
    this.renderStats.totalTasks++

    // 按优先级排序
    this.sortTasksByPriority()

    // 如果当前没有在渲染，开始渲染
    if (!this.isRendering) {
      this.startRendering()
    }
  }

  /**
   * 批量添加任务
   */
  addTasks(tasks: Omit<RenderTask, 'estimatedTime'>[]): void {
    tasks.forEach((task) => {
      const estimatedTime = this.estimateTaskTime(task)
      const fullTask: RenderTask = { ...task, estimatedTime }
      this.taskQueue.push(fullTask)
    })

    this.renderStats.totalTasks += tasks.length
    this.sortTasksByPriority()

    if (!this.isRendering) {
      this.startRendering()
    }
  }

  /**
   * 清空任务队列
   */
  clear(): void {
    this.taskQueue = []
    this.renderStats.totalTasks = 0
    this.renderStats.completedTasks = 0

    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  /**
   * 获取渲染进度
   */
  getProgress(): {
    total: number
    completed: number
    remaining: number
    percentage: number
    isRendering: boolean
  } {
    return {
      total: this.renderStats.totalTasks,
      completed: this.renderStats.completedTasks,
      remaining: this.taskQueue.length,
      percentage:
        this.renderStats.totalTasks > 0
          ? (this.renderStats.completedTasks / this.renderStats.totalTasks) *
            100
          : 0,
      isRendering: this.isRendering,
    }
  }

  /**
   * 获取渲染统计信息
   */
  getStats() {
    return {
      ...this.renderStats,
      queueLength: this.taskQueue.length,
      config: this.config,
    }
  }

  // 私有方法

  private async startRendering(): Promise<void> {
    if (this.isRendering || this.taskQueue.length === 0) {
      return
    }

    this.isRendering = true
    this.abortController = new AbortController()

    try {
      while (
        this.taskQueue.length > 0 &&
        !this.abortController.signal.aborted
      ) {
        const batch = this.createBatch()
        await this.renderBatch(batch)

        // 自适应调整批次大小
        if (this.config.enableAdaptiveBatching) {
          this.adjustBatchSize(batch)
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('[IncrementalRenderer] 渲染过程中发生错误:', error)
      }
    } finally {
      this.isRendering = false
      this.abortController = null
    }
  }

  private createBatch(): RenderBatch {
    const batchSize = this.renderStats.adaptiveBatchSize
    const tasks = this.taskQueue.splice(0, batchSize)
    const totalEstimatedTime = tasks.reduce(
      (sum, task) => sum + task.estimatedTime,
      0
    )

    return {
      tasks,
      totalEstimatedTime,
      startTime: performance.now(),
    }
  }

  private async renderBatch(batch: RenderBatch): Promise<void> {
    return new Promise((resolve, reject) => {
      const renderNextTask = async (index: number) => {
        if (index >= batch.tasks.length) {
          resolve()
          return
        }

        if (this.abortController?.signal.aborted) {
          reject(new Error('Rendering aborted'))
          return
        }

        const task = batch.tasks[index]
        const taskStartTime = performance.now()

        try {
          await task.renderFn(task.data)

          const taskEndTime = performance.now()
          const actualTime = taskEndTime - taskStartTime

          // 更新统计信息
          this.updateTaskStats(actualTime)
          this.renderStats.completedTasks++

          // 检查是否需要让出控制权
          const elapsedTime = taskEndTime - batch.startTime
          if (elapsedTime >= this.config.maxFrameTime) {
            // 使用 scheduler API 或 requestIdleCallback
            this.scheduleNextTask(() => renderNextTask(index + 1))
          } else {
            // 继续渲染下一个任务
            renderNextTask(index + 1)
          }
        } catch (error) {
          console.error(
            `[IncrementalRenderer] 任务 ${task.id} 渲染失败:`,
            error
          )
          // 继续渲染下一个任务
          this.scheduleNextTask(() => renderNextTask(index + 1))
        }
      }

      renderNextTask(0)
    })
  }

  private scheduleNextTask(callback: () => void): void {
    if ('scheduler' in window && 'postTask' in (window as any).scheduler) {
      // 使用现代的 Scheduler API
      ;(window as any).scheduler.postTask(callback, {
        priority: 'user-blocking',
      })
    } else if ('requestIdleCallback' in window) {
      // 使用 requestIdleCallback
      requestIdleCallback(callback, { timeout: 16 })
    } else {
      // 降级到 setTimeout
      setTimeout(callback, 0)
    }
  }

  private sortTasksByPriority(): void {
    this.taskQueue.sort((a, b) => {
      const weightA = this.config.priorityWeights[a.priority] || 1
      const weightB = this.config.priorityWeights[b.priority] || 1
      return weightB - weightA
    })
  }

  private estimateTaskTime(task: Omit<RenderTask, 'estimatedTime'>): number {
    // 基于优先级和数据复杂度估算渲染时间
    const baseTimes = {
      critical: 5,
      high: 8,
      normal: 12,
      low: 16,
    }

    const baseTime = baseTimes[task.priority] || 12

    // 根据数据复杂度调整
    const dataComplexity = this.calculateDataComplexity(task.data)
    return baseTime * dataComplexity
  }

  private calculateDataComplexity(data: any): number {
    if (!data) return 1

    if (typeof data === 'object') {
      const keys = Object.keys(data)
      return Math.min(keys.length / 10 + 1, 3) // 最多3倍复杂度
    }

    return 1
  }

  private updateTaskStats(actualTime: number): void {
    // 使用指数移动平均更新平均任务时间
    const alpha = 0.1
    this.renderStats.averageTaskTime =
      alpha * actualTime + (1 - alpha) * this.renderStats.averageTaskTime
  }

  private adjustBatchSize(batch: RenderBatch): void {
    const actualTime = performance.now() - batch.startTime
    const targetTime = this.config.maxFrameTime * 0.8 // 留20%缓冲

    if (actualTime > targetTime) {
      // 渲染时间过长，减少批次大小
      this.renderStats.adaptiveBatchSize = Math.max(
        this.renderStats.adaptiveBatchSize - 1,
        this.config.minBatchSize
      )
    } else if (actualTime < targetTime * 0.5) {
      // 渲染时间过短，增加批次大小
      this.renderStats.adaptiveBatchSize = Math.min(
        this.renderStats.adaptiveBatchSize + 1,
        this.config.maxBatchSize
      )
    }
  }
}

// 全局增量渲染器实例
export const globalIncrementalRenderer = new IncrementalRenderer()

/**
 * 增量渲染 Hook
 */
export function useIncrementalRenderer() {
  const renderer = globalIncrementalRenderer

  /**
   * 渲染大数据集
   */
  const renderLargeDataset = async (
    data: any[],
    renderItemFn: (item: any, index: number) => Promise<void> | void,
    options: {
      priority?: 'critical' | 'high' | 'normal' | 'low'
      chunkSize?: number
      onProgress?: (progress: any) => void
    } = {}
  ) => {
    const { priority = 'normal', chunkSize = 20, onProgress } = options

    // 清空之前的任务
    renderer.clear()

    // 将数据分块并创建渲染任务
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize)
      const taskId = `chunk-${i}-${i + chunkSize - 1}`

      renderer.addTask({
        id: taskId,
        priority,
        data: { chunk, startIndex: i },
        renderFn: async (taskData) => {
          const { chunk: items, startIndex } = taskData
          for (let j = 0; j < items.length; j++) {
            await renderItemFn(items[j], startIndex + j)
          }

          // 报告进度
          if (onProgress) {
            onProgress(renderer.getProgress())
          }
        },
      })
    }
  }

  /**
   * 渲染可见区域优先
   */
  const renderVisibleFirst = async (
    allData: any[],
    visibleIndices: number[],
    renderItemFn: (item: any, index: number) => Promise<void> | void,
    onProgress?: (progress: any) => void
  ) => {
    renderer.clear()

    // 优先渲染可见项目
    visibleIndices.forEach((index, i) => {
      if (index < allData.length) {
        renderer.addTask({
          id: `visible-${index}`,
          priority: 'critical',
          data: { item: allData[index], index },
          renderFn: async (taskData) => {
            await renderItemFn(taskData.item, taskData.index)
            if (onProgress) {
              onProgress(renderer.getProgress())
            }
          },
        })
      }
    })

    // 然后渲染其他项目
    allData.forEach((item, index) => {
      if (!visibleIndices.includes(index)) {
        renderer.addTask({
          id: `background-${index}`,
          priority: 'low',
          data: { item, index },
          renderFn: async (taskData) => {
            await renderItemFn(taskData.item, taskData.index)
            if (onProgress) {
              onProgress(renderer.getProgress())
            }
          },
        })
      }
    })
  }

  return {
    renderer,
    renderLargeDataset,
    renderVisibleFirst,
    getProgress: () => renderer.getProgress(),
    getStats: () => renderer.getStats(),
    clear: () => renderer.clear(),
  }
}

// 在开发环境暴露调试接口
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__INCREMENTAL_RENDERER__ = {
    renderer: globalIncrementalRenderer,
    progress: () => console.log(globalIncrementalRenderer.getProgress()),
    stats: () => console.table(globalIncrementalRenderer.getStats()),
    clear: () => globalIncrementalRenderer.clear(),
  }
}
