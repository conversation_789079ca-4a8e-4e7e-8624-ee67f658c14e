/**
 * 渲染优先级队列 - 按重要性排序渲染任务
 *
 * 主要特性：
 * 1. 多级优先级队列
 * 2. 可见区域优先渲染
 * 3. 自适应调度策略
 * 4. 渲染性能监控
 */

interface RenderTask {
  id: string
  priority: 'critical' | 'high' | 'normal' | 'low'
  element: HTMLElement | null
  renderFn: () => Promise<void> | void
  estimatedTime: number
  isVisible: boolean
  retryCount: number
  createdAt: number
}

interface ViewportInfo {
  top: number
  bottom: number
  left: number
  right: number
  height: number
  width: number
}

interface QueueStats {
  totalTasks: number
  completedTasks: number
  failedTasks: number
  averageRenderTime: number
  queuesByPriority: Record<string, number>
  visibleTasksCount: number
}

interface PriorityQueueConfig {
  maxConcurrentTasks: number
  frameTimeLimit: number // 每帧最大渲染时间（毫秒）
  visibilityCheckInterval: number // 可见性检查间隔（毫秒）
  retryLimit: number
  enableVisibilityOptimization: boolean
  enablePerformanceMonitoring: boolean
}

const DEFAULT_CONFIG: PriorityQueueConfig = {
  maxConcurrentTasks: 3,
  frameTimeLimit: 16, // 60fps
  visibilityCheckInterval: 100,
  retryLimit: 2,
  enableVisibilityOptimization: true,
  enablePerformanceMonitoring: true,
}

export class RenderPriorityQueue {
  private config: PriorityQueueConfig
  private queues: Record<string, RenderTask[]> = {
    critical: [],
    high: [],
    normal: [],
    low: [],
  }
  private activeTasks = new Set<string>()
  private stats: QueueStats
  private isProcessing = false
  private viewportInfo: ViewportInfo | null = null
  private intersectionObserver: IntersectionObserver | null = null
  private visibilityCheckTimer: number | null = null

  constructor(config: Partial<PriorityQueueConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.stats = this.initializeStats()

    if (this.config.enableVisibilityOptimization) {
      this.initializeVisibilityTracking()
    }
  }

  /**
   * 添加渲染任务
   */
  addTask(
    task: Omit<RenderTask, 'createdAt' | 'retryCount' | 'isVisible'>
  ): void {
    const fullTask: RenderTask = {
      ...task,
      createdAt: Date.now(),
      retryCount: 0,
      isVisible: this.checkElementVisibility(task.element),
    }

    this.queues[task.priority].push(fullTask)
    this.stats.totalTasks++
    this.stats.queuesByPriority[task.priority]++

    if (fullTask.isVisible) {
      this.stats.visibleTasksCount++
    }

    // 如果是关键任务或可见任务，立即开始处理
    if (task.priority === 'critical' || fullTask.isVisible) {
      this.startProcessing()
    } else if (!this.isProcessing) {
      // 延迟处理非关键任务
      setTimeout(() => this.startProcessing(), 0)
    }
  }

  /**
   * 批量添加任务
   */
  addTasks(
    tasks: Array<Omit<RenderTask, 'createdAt' | 'retryCount' | 'isVisible'>>
  ): void {
    tasks.forEach((task) => this.addTask(task))
  }

  /**
   * 清空队列
   */
  clear(): void {
    Object.keys(this.queues).forEach((priority) => {
      this.queues[priority] = []
    })
    this.activeTasks.clear()
    this.stats = this.initializeStats()
  }

  /**
   * 暂停处理
   */
  pause(): void {
    this.isProcessing = false
  }

  /**
   * 恢复处理
   */
  resume(): void {
    if (!this.isProcessing && this.hasTasksToProcess()) {
      this.startProcessing()
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): QueueStats {
    return { ...this.stats }
  }

  /**
   * 更新视口信息
   */
  updateViewport(viewport: ViewportInfo): void {
    this.viewportInfo = viewport
    this.updateTaskVisibility()
  }

  /**
   * 销毁队列
   */
  destroy(): void {
    this.clear()

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
      this.intersectionObserver = null
    }

    if (this.visibilityCheckTimer) {
      clearInterval(this.visibilityCheckTimer)
      this.visibilityCheckTimer = null
    }
  }

  // 私有方法

  private async startProcessing(): Promise<void> {
    if (this.isProcessing) return

    this.isProcessing = true

    try {
      while (
        this.hasTasksToProcess() &&
        this.activeTasks.size < this.config.maxConcurrentTasks
      ) {
        const task = this.getNextTask()
        if (!task) break

        this.processTask(task)
      }
    } catch (error) {
      console.error('[RenderPriorityQueue] 处理队列时发生错误:', error)
    } finally {
      // 如果还有任务且没有达到并发限制，继续处理
      if (
        this.hasTasksToProcess() &&
        this.activeTasks.size < this.config.maxConcurrentTasks
      ) {
        setTimeout(() => this.startProcessing(), 0)
      } else {
        this.isProcessing = false
      }
    }
  }

  private async processTask(task: RenderTask): Promise<void> {
    this.activeTasks.add(task.id)
    const startTime = performance.now()

    try {
      await task.renderFn()

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // 更新统计信息
      this.updateRenderStats(renderTime)
      this.stats.completedTasks++

      if (import.meta.env.DEV && this.config.enablePerformanceMonitoring) {
        console.log(
          `[RenderPriorityQueue] 任务 ${task.id} 完成，耗时 ${renderTime.toFixed(2)}ms`
        )
      }
    } catch (error) {
      console.error(`[RenderPriorityQueue] 任务 ${task.id} 失败:`, error)

      // 重试逻辑
      if (task.retryCount < this.config.retryLimit) {
        task.retryCount++
        task.priority =
          task.priority === 'critical'
            ? 'high'
            : task.priority === 'high'
              ? 'normal'
              : 'low'
        this.queues[task.priority].push(task)
      } else {
        this.stats.failedTasks++
      }
    } finally {
      this.activeTasks.delete(task.id)

      // 检查是否需要继续处理
      if (this.hasTasksToProcess()) {
        this.scheduleNextProcessing()
      }
    }
  }

  private getNextTask(): RenderTask | null {
    // 优先级顺序：critical > high > normal > low
    const priorities = ['critical', 'high', 'normal', 'low'] as const

    for (const priority of priorities) {
      const queue = this.queues[priority]
      if (queue.length === 0) continue

      // 在同一优先级内，优先处理可见任务
      let taskIndex = -1

      if (this.config.enableVisibilityOptimization) {
        taskIndex = queue.findIndex((task) => task.isVisible)
      }

      if (taskIndex === -1) {
        taskIndex = 0 // 如果没有可见任务，取第一个
      }

      const task = queue.splice(taskIndex, 1)[0]
      this.stats.queuesByPriority[priority]--

      if (task.isVisible) {
        this.stats.visibleTasksCount--
      }

      return task
    }

    return null
  }

  private hasTasksToProcess(): boolean {
    return Object.values(this.queues).some((queue) => queue.length > 0)
  }

  private scheduleNextProcessing(): void {
    if ('scheduler' in window && 'postTask' in (window as any).scheduler) {
      ;(window as any).scheduler.postTask(() => this.startProcessing(), {
        priority: 'user-blocking',
      })
    } else {
      requestAnimationFrame(() => this.startProcessing())
    }
  }

  private checkElementVisibility(element: HTMLElement | null): boolean {
    if (!element || !this.viewportInfo) return false

    const rect = element.getBoundingClientRect()
    const viewport = this.viewportInfo

    return (
      rect.bottom > viewport.top &&
      rect.top < viewport.bottom &&
      rect.right > viewport.left &&
      rect.left < viewport.right
    )
  }

  private updateTaskVisibility(): void {
    Object.values(this.queues).forEach((queue) => {
      queue.forEach((task) => {
        const wasVisible = task.isVisible
        task.isVisible = this.checkElementVisibility(task.element)

        if (!wasVisible && task.isVisible) {
          this.stats.visibleTasksCount++
        } else if (wasVisible && !task.isVisible) {
          this.stats.visibleTasksCount--
        }
      })
    })
  }

  private initializeVisibilityTracking(): void {
    // 使用 Intersection Observer 跟踪元素可见性
    if ('IntersectionObserver' in window) {
      this.intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            const element = entry.target as HTMLElement
            const isVisible = entry.isIntersecting

            // 更新相关任务的可见性
            Object.values(this.queues).forEach((queue) => {
              queue.forEach((task) => {
                if (task.element === element) {
                  const wasVisible = task.isVisible
                  task.isVisible = isVisible

                  if (!wasVisible && isVisible) {
                    this.stats.visibleTasksCount++
                  } else if (wasVisible && !isVisible) {
                    this.stats.visibleTasksCount--
                  }
                }
              })
            })
          })
        },
        {
          rootMargin: '50px', // 提前50px开始预加载
          threshold: 0.1, // 10%可见时触发
        }
      )
    }

    // 定期检查可见性
    this.visibilityCheckTimer = setInterval(() => {
      this.updateTaskVisibility()
    }, this.config.visibilityCheckInterval)
  }

  private initializeStats(): QueueStats {
    return {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageRenderTime: 0,
      queuesByPriority: {
        critical: 0,
        high: 0,
        normal: 0,
        low: 0,
      },
      visibleTasksCount: 0,
    }
  }

  private updateRenderStats(renderTime: number): void {
    const alpha = 0.1 // 指数移动平均
    this.stats.averageRenderTime =
      alpha * renderTime + (1 - alpha) * this.stats.averageRenderTime
  }
}

// 全局渲染优先级队列实例
export const globalRenderPriorityQueue = new RenderPriorityQueue()

/**
 * 渲染优先级队列 Hook
 */
export function useRenderPriorityQueue() {
  const queue = globalRenderPriorityQueue

  /**
   * 添加高优先级渲染任务（可见区域）
   */
  const addCriticalTask = (
    id: string,
    element: HTMLElement | null,
    renderFn: () => Promise<void> | void,
    estimatedTime = 16
  ) => {
    queue.addTask({
      id,
      priority: 'critical',
      element,
      renderFn,
      estimatedTime,
    })
  }

  /**
   * 添加普通渲染任务
   */
  const addNormalTask = (
    id: string,
    element: HTMLElement | null,
    renderFn: () => Promise<void> | void,
    estimatedTime = 16
  ) => {
    queue.addTask({
      id,
      priority: 'normal',
      element,
      renderFn,
      estimatedTime,
    })
  }

  /**
   * 添加低优先级渲染任务（非可见区域）
   */
  const addLowPriorityTask = (
    id: string,
    element: HTMLElement | null,
    renderFn: () => Promise<void> | void,
    estimatedTime = 16
  ) => {
    queue.addTask({
      id,
      priority: 'low',
      element,
      renderFn,
      estimatedTime,
    })
  }

  /**
   * 批量添加可见区域任务
   */
  const addVisibleTasks = (
    tasks: Array<{
      id: string
      element: HTMLElement | null
      renderFn: () => Promise<void> | void
      estimatedTime?: number
    }>
  ) => {
    const renderTasks = tasks.map((task) => ({
      ...task,
      priority: 'critical' as const,
      estimatedTime: task.estimatedTime || 16,
    }))
    queue.addTasks(renderTasks)
  }

  /**
   * 更新视口信息
   */
  const updateViewport = (container: HTMLElement) => {
    const rect = container.getBoundingClientRect()
    queue.updateViewport({
      top: rect.top,
      bottom: rect.bottom,
      left: rect.left,
      right: rect.right,
      height: rect.height,
      width: rect.width,
    })
  }

  return {
    addCriticalTask,
    addNormalTask,
    addLowPriorityTask,
    addVisibleTasks,
    updateViewport,
    getStats: () => queue.getStats(),
    clear: () => queue.clear(),
    pause: () => queue.pause(),
    resume: () => queue.resume(),
  }
}

// 在开发环境暴露调试接口
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__RENDER_PRIORITY_QUEUE__ = {
    queue: globalRenderPriorityQueue,
    stats: () => console.table(globalRenderPriorityQueue.getStats()),
    clear: () => globalRenderPriorityQueue.clear(),
    pause: () => globalRenderPriorityQueue.pause(),
    resume: () => globalRenderPriorityQueue.resume(),
  }
}
