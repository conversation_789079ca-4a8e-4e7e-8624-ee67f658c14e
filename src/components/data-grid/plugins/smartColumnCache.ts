/**
 * 智能列缓存系统
 *
 * 主要功能：
 * 1. 列配置智能缓存和复用
 * 2. 基于内容哈希的缓存键
 * 3. 内存使用优化
 * 4. 自动清理和垃圾回收
 */

import { reactive, computed, shallowRef, type Ref } from 'vue'
import type { PluginColumnConfig } from './types'

interface CacheEntry {
  config: any
  hash: string
  usageCount: number
  lastAccessed: number
  memorySize: number
}

interface CacheStats {
  totalEntries: number
  totalMemory: number
  hitRate: number
  missRate: number
  avgUsageCount: number
}

export class SmartColumnCache {
  private cache = new Map<string, CacheEntry>()
  private hashToKey = new Map<string, string>()
  private accessHistory = new Map<string, number[]>()

  // 统计信息
  private stats = reactive({
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
  })

  // 配置选项
  private config = {
    maxEntries: 200,
    maxMemoryMB: 50,
    ttl: 15 * 60 * 1000, // 15分钟
    cleanupInterval: 3 * 60 * 1000, // 3分钟清理一次
  }

  private cleanupTimer: number | null = null

  constructor(config?: Partial<typeof SmartColumnCache.prototype.config>) {
    if (config) {
      Object.assign(this.config, config)
    }

    this.startCleanupTimer()
  }

  /**
   * 获取或创建列配置
   */
  getOrCreate<T = PluginColumnConfig>(key: string, factory: () => T): T {
    this.stats.totalRequests++

    // 首先检查缓存
    const cached = this.get(key)
    if (cached) {
      this.stats.hits++
      return cached as T
    }

    // 缓存未命中，创建新配置
    this.stats.misses++
    const config = factory()
    this.set(key, config as any)

    return config
  }

  /**
   * 从缓存获取配置
   */
  private get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.remove(key)
      return null
    }

    // 更新访问信息
    entry.usageCount++
    entry.lastAccessed = Date.now()
    this.recordAccess(key)

    return entry.config
  }

  /**
   * 设置缓存条目
   */
  private set(key: string, config: any): void {
    const hash = this.generateHash(config)

    // 检查是否已有相同配置（去重）
    const existingKey = this.hashToKey.get(hash)
    if (existingKey && this.cache.has(existingKey)) {
      // 复用现有配置
      const existingEntry = this.cache.get(existingKey)!
      this.cache.set(key, existingEntry)
      this.recordAccess(key)
      return
    }

    const memorySize = this.estimateMemorySize(config)
    const entry: CacheEntry = {
      config,
      hash,
      usageCount: 1,
      lastAccessed: Date.now(),
      memorySize,
    }

    // 检查容量限制
    this.ensureCapacity(memorySize)

    // 添加到缓存
    this.cache.set(key, entry)
    this.hashToKey.set(hash, key)
    this.recordAccess(key)
  }

  /**
   * 移除缓存条目
   */
  private remove(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    this.cache.delete(key)
    this.hashToKey.delete(entry.hash)
    this.accessHistory.delete(key)

    return true
  }

  /**
   * 生成配置哈希
   */
  private generateHash(config: any): string {
    // 提取关键字段用于哈希计算
    const keyFields = {
      field: config.field,
      title: config.title,
      plugin: config.plugin,
      width: config.width,
      pluginConfig: config.pluginConfig,
    }

    const str = JSON.stringify(keyFields, Object.keys(keyFields).sort())
    return this.simpleHash(str)
  }

  private simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 估算内存大小
   */
  private estimateMemorySize(config: any): number {
    // 简单的内存估算（单位：字节）
    const str = JSON.stringify(config)
    return str.length * 2 // 每个字符约2字节
  }

  /**
   * 检查是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.lastAccessed > this.config.ttl
  }

  /**
   * 记录访问历史
   */
  private recordAccess(key: string): void {
    const history = this.accessHistory.get(key) || []
    const now = Date.now()

    // 只保留最近100次访问记录
    history.push(now)
    if (history.length > 100) {
      history.shift()
    }

    this.accessHistory.set(key, history)
  }

  /**
   * 确保缓存容量
   */
  private ensureCapacity(newEntrySize: number): void {
    const currentMemory = this.getTotalMemoryUsage()
    const maxMemoryBytes = this.config.maxMemoryMB * 1024 * 1024

    // 检查内存限制
    if (currentMemory + newEntrySize > maxMemoryBytes) {
      this.evictByMemory(newEntrySize)
    }

    // 检查条目数量限制
    if (this.cache.size >= this.config.maxEntries) {
      this.evictByCount()
    }
  }

  /**
   * 按内存使用进行淘汰
   */
  private evictByMemory(requiredSpace: number): void {
    const entries = Array.from(this.cache.entries())

    // 按优先级排序（使用频率低 + 最后访问时间早的优先淘汰）
    entries.sort(([, a], [, b]) => {
      const scoreA = a.usageCount * 0.7 + (Date.now() - a.lastAccessed) * 0.3
      const scoreB = b.usageCount * 0.7 + (Date.now() - b.lastAccessed) * 0.3
      return scoreA - scoreB
    })

    let freedMemory = 0
    for (const [key] of entries) {
      if (freedMemory >= requiredSpace) break

      const entry = this.cache.get(key)!
      freedMemory += entry.memorySize
      this.remove(key)
      this.stats.evictions++
    }
  }

  /**
   * 按数量进行淘汰
   */
  private evictByCount(): void {
    const entries = Array.from(this.cache.entries())

    // 移除最少使用的条目
    const toRemove = entries
      .sort(([, a], [, b]) => a.usageCount - b.usageCount)
      .slice(0, Math.floor(this.config.maxEntries * 0.1)) // 移除10%

    toRemove.forEach(([key]) => {
      this.remove(key)
      this.stats.evictions++
    })
  }

  /**
   * 获取总内存使用量
   */
  private getTotalMemoryUsage(): number {
    return Array.from(this.cache.values()).reduce(
      (total, entry) => total + entry.memorySize,
      0
    )
  }

  /**
   * 定期清理
   */
  private cleanup(): void {
    const now = Date.now()
    const toRemove: string[] = []

    this.cache.forEach((entry, key) => {
      if (this.isExpired(entry)) {
        toRemove.push(key)
      }
    })

    toRemove.forEach((key) => {
      this.remove(key)
    })

    if (toRemove.length > 0) {
      console.log(`🧹 [SmartColumnCache] 清理了 ${toRemove.length} 个过期条目`)
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const hitRate =
      this.stats.totalRequests > 0
        ? (this.stats.hits / this.stats.totalRequests) * 100
        : 0

    const avgUsageCount =
      this.cache.size > 0
        ? Array.from(this.cache.values()).reduce(
            (sum, entry) => sum + entry.usageCount,
            0
          ) / this.cache.size
        : 0

    return {
      totalEntries: this.cache.size,
      totalMemory: this.getTotalMemoryUsage(),
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round((100 - hitRate) * 100) / 100,
      avgUsageCount: Math.round(avgUsageCount * 100) / 100,
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.hashToKey.clear()
    this.accessHistory.clear()
    this.stats.hits = 0
    this.stats.misses = 0
    this.stats.evictions = 0
    this.stats.totalRequests = 0
  }

  /**
   * 销毁缓存
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.clear()
  }
}

// 全局智能列缓存实例
export const globalColumnCache = new SmartColumnCache()

// 在开发环境暴露调试接口
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__COLUMN_CACHE__ = {
    cache: globalColumnCache,
    stats: () => console.log(globalColumnCache.getStats()),
    clear: () => globalColumnCache.clear(),
  }
}
