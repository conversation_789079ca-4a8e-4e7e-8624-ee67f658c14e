/**
 * 插件管理器
 *
 * 提供轻量级的组件注册和列配置功能，替代复杂的插件系统。
 * 支持状态、布尔值、链接、操作等常用渲染器，以及丰富的 Helper 方法。
 *
 * @example
 * ```typescript
 * const manager = new PluginManager()
 * const columnHelper = manager.getColumnHelper()
 *
 * // 创建状态列
 * const statusColumn = columnHelper.status('status', '状态', {
 *   statusMap: { active: { text: '活跃', type: 'success' } }
 * })
 * ```
 */

import { markRaw, type Component } from 'vue'
import type { BaseRendererConfig, PluginColumnConfig } from './types'
import { StatusRendererConfig } from './renderers/status'
import { BooleanRendererConfig } from './renderers/boolean'
import { LinkRendererConfig } from './renderers/link'
import { ActionsRendererConfig } from './renderers/actions'
import { CompositeRendererConfig } from './renderers/composite'
import { DateRendererConfig } from './renderers/date'
import { NumberRendererConfig } from './renderers/number'
import { RelationRendererConfig } from './renderers/relation'
import { UserRendererConfig } from './renderers/user'

// 渲染器定义
export interface PluginRenderer {
  name: string
  component: Component
  defaultConfig?: Record<string, any>
  defaultWidth?: number
}

/**
 * 插件管理器类
 */
export class PluginManager {
  private components = new Map<string, Component>()
  private renderers = new Map<string, PluginRenderer>()
  private columnHelper: PluginHelper

  constructor() {
    this.columnHelper = new PluginHelper(this)
  }

  /**
   * 注册组件
   */
  registerComponent(name: string, component: Component): void {
    this.components.set(name, markRaw(component))
  }

  /**
   * 获取组件
   */
  getComponent(name: string): Component | undefined {
    return this.components.get(name)
  }

  /**
   * 注册渲染器
   */
  registerRenderer(renderer: PluginRenderer): void {
    this.renderers.set(renderer.name, renderer)
    // 同时注册组件
    this.registerComponent(renderer.name, renderer.component)
  }

  /**
   * 获取渲染器
   */
  getRenderer(name: string): PluginRenderer | undefined {
    return this.renderers.get(name)
  }

  /**
   * 获取列助手
   */
  getColumnHelper(): PluginHelper {
    return this.columnHelper
  }

  /**
   * 获取所有已注册的组件名称
   */
  getRegisteredComponents(): string[] {
    return Array.from(this.components.keys())
  }

  /**
   * 获取所有已注册的组件 Map
   */
  getAllComponents(): Map<string, Component> {
    return new Map(this.components)
  }

  /**
   * 获取所有已注册的渲染器名称
   */
  getRegisteredRenderers(): string[] {
    return Array.from(this.renderers.keys())
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.components.clear()
    this.renderers.clear()
  }
}

/**
 * 列助手类
 */
export class PluginHelper {
  constructor(private manager: PluginManager) {}

  /**
   * 创建基础列配置
   */
  createColumn(
    field: string,
    title: string,
    plugin?: string,
    config?: BaseRendererConfig
  ): PluginColumnConfig {
    const column: PluginColumnConfig = {
      field,
      title,
    }

    if (plugin) {
      const renderer = this.manager.getRenderer(plugin)
      if (!renderer) {
        console.warn(`[PluginHelper] 渲染器 "${plugin}" 未注册。`)
        return column
      }

      const pluginConfig = {
        ...renderer.defaultConfig,
        ...config,
      }

      column.plugin = plugin
      column.pluginConfig = pluginConfig
      if (renderer.defaultWidth) {
        column.width = config?.width || renderer.defaultWidth
      }

      // 直接在列配置中提供组件和 props
      column.slots = {
        default: {
          component: markRaw(renderer.component),
          props: {
            config: pluginConfig,
          },
        },
      }
    }

    // 处理其他列配置属性（如 width, fixed, align 等）
    if (config) {
      Object.keys(config).forEach((key) => {
        if (key !== 'width' && !column.hasOwnProperty(key)) {
          column[key] = config[key]
        }
      })
    }

    return column
  }

  /**
   * 创建状态列
   */
  status(
    field: string,
    title: string,
    config?: StatusRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'StatusRenderer', {
      variant: 'badge',
      autoFromMetadata: config?.statusMap ? false : true,
      ...config,
    })
  }

  /**
   * 创建布尔值列
   */
  boolean(
    field: string,
    title: string,
    config?: BooleanRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'BooleanRenderer', {
      variant: 'badge',
      trueText: '是',
      falseText: '否',
      ...config,
    })
  }

  /**
   * 创建链接列
   */
  link(
    field: string,
    title: string,
    config?: LinkRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showExternal: true,
      truncate: false,
      ...config,
    })
  }

  mail(field: string, title: string, config?: LinkRendererConfig) {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showTypeIcon: true,
      showExternal: true,
      truncate: true,
      linkType: 'mail',
      ...config,
    })
  }

  phone(field: string, title: string, config?: LinkRendererConfig) {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showExternal: false,
      showTypeIcon: true,
      truncate: false,
      linkType: 'phone',
      ...config,
    })
  }

  /**
   * 创建操作列
   */
  actions(title: string, config?: ActionsRendererConfig): PluginColumnConfig {
    return this.createColumn('actions', title, 'ActionsRenderer', {
      layout: 'horizontal',
      actions: [],
      ...config,
    })
  }

  /**
   * 创建复合列 - 支持图标、主内容、子内容和操作按钮的组合显示
   */
  composite(
    field: string,
    title: string,
    config?: CompositeRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'CompositeRenderer', {
      main: { field: field },
      subs: { items: [], layout: 'horizontal', separator: '·' },
      showActionsCount: 1,
      enableHover: true,
      ...config,
    })
  }

  /**
   * 创建日期列
   */
  date(
    field: string,
    title: string,
    config?: DateRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      format: 'yyyy-MM-dd',
      variant: 'text',
      ...config,
    })
  }

  /**
   * 创建日期时间列
   */
  datetime(
    field: string,
    title: string,
    config?: DateRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      format: 'full-datetime',
      variant: 'text',
      ...config,
    })
  }

  /**
   * 创建相对时间列
   */
  relativeTime(
    field: string,
    title: string,
    config?: DateRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      format: 'relative',
      variant: 'relative-tooltip',
      tooltip: {
        enabled: true,
        format: 'full-datetime',
        extraInfo: {
          showWeekday: true,
          showRelative: false,
        },
      },
      ...config,
    })
  }

  /**
   * 创建智能时间列（今天显示时间，其他显示日期）
   */
  smartTime(
    field: string,
    title: string,
    config?: DateRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      format: 'smart',
      variant: 'tooltip',
      tooltip: {
        enabled: true,
        format: 'full-datetime',
        extraInfo: {
          showWeekday: true,
          showRelative: true,
        },
      },
      ...config,
    })
  }

  /**
   * 创建时间戳列（带有徽章样式）
   */
  timestamp(
    field: string,
    title: string,
    config?: DateRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      format: 'full-datetime',
      variant: 'badge',
      theme: {
        badgeColor: 'info',
        fontSize: 'xs',
      },
      ...config,
    })
  }

  /**
   * 创建数值列
   */
  number(
    field: string,
    title: string,
    config?: NumberRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'NumberRenderer', {
      numberType: 'decimal',
      variant: 'text',
      format: {
        decimalPlaces: 2,
        useGrouping: true,
      },
      nullText: '--',
      ...config,
    })
  }

  /**
   * 创建整数列
   */
  integer(
    field: string,
    title: string,
    config?: NumberRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'NumberRenderer', {
      numberType: 'integer',
      variant: 'text',
      format: {
        useGrouping: true,
      },
      nullText: '--',
      ...config,
    })
  }

  /**
   * 创建货币列
   */
  currency(
    field: string,
    title: string,
    config?: NumberRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'NumberRenderer', {
      numberType: 'currency',
      variant: 'text',
      format: {
        decimalPlaces: 2,
        useGrouping: true,
      },
      nullText: '--',
      ...config,
    })
  }

  /**
   * 创建百分比列
   */
  percentage(
    field: string,
    title: string,
    config?: NumberRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'NumberRenderer', {
      numberType: 'percentage',
      variant: 'text',
      format: {
        decimalPlaces: 1,
        useGrouping: false,
      },
      nullText: '--',
      ...config,
    })
  }

  /**
   * 创建进度条列
   */
  progress(
    field: string,
    title: string,
    config?: NumberRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'NumberRenderer', {
      numberType: 'percentage',
      variant: 'progress',
      progress: {
        min: 0,
        max: 100,
        showText: true,
        color: 'blue',
      },
      nullText: '--',
      ...config,
    })
  }

  /**
   * 创建带颜色的数值列（正负数不同颜色）
   */
  coloredNumber(
    field: string,
    title: string,
    config?: NumberRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'NumberRenderer', {
      numberType: 'decimal',
      variant: 'colored',
      format: {
        decimalPlaces: 2,
        useGrouping: true,
      },
      colors: {
        positiveColor: 'green-600',
        negativeColor: 'red-600',
        zeroColor: 'gray-500',
      },
      nullText: '--',
      ...config,
    })
  }

  /**
   * 创建货币换算列
   */
  exchangeCurrency(
    field: string,
    title: string,
    currencyConfig: { code: string; exchange_rate: string; symbol: string },
    config?: NumberRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'NumberRenderer', {
      numberType: 'currency',
      variant: 'text',
      currency: currencyConfig,
      showDualCurrency: true,
      dualCurrencyMode: 'primary-secondary',
      format: {
        decimalPlaces: 2,
        useGrouping: true,
      },
      nullText: '--',
      ...config,
    })
  }

  /**
   * 创建关系列
   */
  relation(
    field: string,
    title: string,
    config?: RelationRendererConfig
  ): PluginColumnConfig {
    const column = this.createColumn(field, title, 'RelationRenderer', {
      variant: 'text',
      autoFromMetadata: true,
      ...config,
    })
    // 关系列默认左对齐
    column.align = 'left'
    return column
  }

  /**
   * 创建关系列表列
   */
  relationList(
    field: string,
    title: string,
    config?: RelationRendererConfig
  ): PluginColumnConfig {
    const column = this.relation(field, title, {
      overflowMode: 'tooltip',
      maxDisplay: 2,
      ...config,
    })
    // 关系列表默认左对齐
    column.align = 'left'
    return column
  }

  /**
   * 创建用户列
   */
  user(
    field: string,
    title: string,
    config?: UserRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'UserRenderer', {
      variant: 'text',
      displayFormat: 'username',
      loadingText: '加载中...',
      errorText: '加载失败',
      nullText: '--',
      enableCache: true,
      showTooltip: false,
      ...config,
    })
  }

  /**
   * 创建用户头像列
   */
  userAvatar(
    field: string,
    title: string,
    config?: UserRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'UserRenderer', {
      variant: 'avatar',
      displayFormat: 'username',
      showAvatar: true,
      avatarSize: 'md',
      avatarFallback: 'initials',
      showTooltip: true,
      tooltipContent: 'userinfo',
      loadingText: '加载中...',
      errorText: '加载失败',
      nullText: '--',
      enableCache: true,
      ...config,
    })
  }

  /**
   * 创建用户徽章列
   */
  userBadge(
    field: string,
    title: string,
    config?: UserRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'UserRenderer', {
      variant: 'badge',
      displayFormat: 'username',
      showTooltip: true,
      tooltipContent: 'userinfo',
      loadingText: '加载中...',
      errorText: '加载失败',
      nullText: '--',
      enableCache: true,
      ...config,
    })
  }

  /**
   * 创建用户链接列
   */
  userLink(
    field: string,
    title: string,
    config?: UserRendererConfig
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'UserRenderer', {
      variant: 'link',
      displayFormat: 'username',
      showTooltip: true,
      tooltipContent: 'userinfo',
      loadingText: '加载中...',
      errorText: '加载失败',
      nullText: '--',
      enableCache: true,
      onClick:
        config?.onClick ||
        ((user, row) => {
          console.log('用户点击:', user, row)
        }),
      ...config,
    })
  }
}
