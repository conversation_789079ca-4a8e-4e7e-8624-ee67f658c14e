/**
 * 高级组件实例池 - 第二阶段性能优化
 * 
 * 主要特性：
 * 1. 智能预热和动态调整池大小
 * 2. 组件实例生命周期管理
 * 3. 内存使用监控和自动清理
 * 4. 性能统计和分析
 */

import { markRaw, type Component, shallowRef, type Ref } from 'vue'

interface PoolStats {
  totalCreated: number
  totalReused: number
  currentPoolSize: number
  maxPoolSize: number
  hitRate: number
  memoryUsage: number
}

interface ComponentInstance {
  component: Component
  createdAt: number
  lastUsed: number
  useCount: number
  isActive: boolean
}

interface PoolConfig {
  maxSize: number
  minSize: number
  maxIdleTime: number // 最大空闲时间（毫秒）
  warmupCount: number // 预热数量
  enableAutoCleanup: boolean
  cleanupInterval: number // 清理间隔（毫秒）
}

const DEFAULT_POOL_CONFIG: PoolConfig = {
  maxSize: 20,
  minSize: 3,
  maxIdleTime: 5 * 60 * 1000, // 5分钟
  warmupCount: 5,
  enableAutoCleanup: true,
  cleanupInterval: 2 * 60 * 1000, // 2分钟
}

export class AdvancedComponentPool {
  private pools = new Map<string, ComponentInstance[]>()
  private activeInstances = new Map<string, Set<ComponentInstance>>()
  private stats = new Map<string, PoolStats>()
  private config: PoolConfig
  private cleanupTimer: number | null = null

  constructor(config: Partial<PoolConfig> = {}) {
    this.config = { ...DEFAULT_POOL_CONFIG, ...config }
    
    if (this.config.enableAutoCleanup) {
      this.startAutoCleanup()
    }
  }

  /**
   * 获取组件实例
   */
  acquire(componentName: string, componentFactory: () => Component): Component {
    const pool = this.getOrCreatePool(componentName)
    const stats = this.getOrCreateStats(componentName)
    
    // 尝试从池中获取可用实例
    const availableInstance = pool.find(instance => !instance.isActive)
    
    if (availableInstance) {
      // 复用现有实例
      availableInstance.isActive = true
      availableInstance.lastUsed = Date.now()
      availableInstance.useCount++
      
      stats.totalReused++
      this.updateHitRate(componentName)
      
      // 添加到活跃实例集合
      this.getActiveInstances(componentName).add(availableInstance)
      
      return availableInstance.component
    }
    
    // 池中没有可用实例，创建新实例
    if (pool.length < this.config.maxSize) {
      const newInstance = this.createInstance(componentFactory())
      pool.push(newInstance)
      
      newInstance.isActive = true
      stats.totalCreated++
      this.updateHitRate(componentName)
      
      // 添加到活跃实例集合
      this.getActiveInstances(componentName).add(newInstance)
      
      return newInstance.component
    }
    
    // 池已满，直接创建临时实例（不加入池）
    console.warn(`[AdvancedComponentPool] 池 ${componentName} 已满，创建临时实例`)
    return componentFactory()
  }

  /**
   * 释放组件实例
   */
  release(componentName: string, component: Component): void {
    const pool = this.getOrCreatePool(componentName)
    const activeInstances = this.getActiveInstances(componentName)
    
    // 查找对应的实例
    const instance = pool.find(inst => inst.component === component)
    
    if (instance && instance.isActive) {
      instance.isActive = false
      instance.lastUsed = Date.now()
      activeInstances.delete(instance)
    }
  }

  /**
   * 预热组件池
   */
  warmup(componentName: string, componentFactory: () => Component): void {
    const pool = this.getOrCreatePool(componentName)
    const currentSize = pool.length
    const targetSize = Math.min(this.config.warmupCount, this.config.maxSize)
    
    for (let i = currentSize; i < targetSize; i++) {
      const instance = this.createInstance(componentFactory())
      pool.push(instance)
    }
    
    if (import.meta.env.DEV) {
      console.log(`[AdvancedComponentPool] 预热 ${componentName} 池，创建 ${targetSize - currentSize} 个实例`)
    }
  }

  /**
   * 获取池统计信息
   */
  getStats(componentName?: string): PoolStats | Map<string, PoolStats> {
    if (componentName) {
      return this.getOrCreateStats(componentName)
    }
    return new Map(this.stats)
  }

  /**
   * 清理过期实例
   */
  cleanup(componentName?: string): void {
    const now = Date.now()
    const poolsToClean = componentName ? [componentName] : Array.from(this.pools.keys())
    
    poolsToClean.forEach(name => {
      const pool = this.pools.get(name)
      if (!pool) return
      
      const activeCount = pool.filter(inst => inst.isActive).length
      const minRequired = Math.max(this.config.minSize, activeCount)
      
      // 移除过期的非活跃实例
      const validInstances = pool.filter(instance => {
        if (instance.isActive) return true
        
        const isExpired = now - instance.lastUsed > this.config.maxIdleTime
        const canRemove = pool.length > minRequired
        
        return !(isExpired && canRemove)
      })
      
      const removedCount = pool.length - validInstances.length
      if (removedCount > 0) {
        this.pools.set(name, validInstances)
        
        if (import.meta.env.DEV) {
          console.log(`[AdvancedComponentPool] 清理 ${name} 池，移除 ${removedCount} 个过期实例`)
        }
      }
    })
  }

  /**
   * 销毁池
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    
    this.pools.clear()
    this.activeInstances.clear()
    this.stats.clear()
  }

  /**
   * 获取内存使用估算
   */
  getMemoryUsage(): number {
    let totalMemory = 0
    
    this.pools.forEach(pool => {
      // 每个组件实例估算 1KB
      totalMemory += pool.length * 1024
    })
    
    return totalMemory
  }

  // 私有方法

  private getOrCreatePool(componentName: string): ComponentInstance[] {
    if (!this.pools.has(componentName)) {
      this.pools.set(componentName, [])
    }
    return this.pools.get(componentName)!
  }

  private getOrCreateStats(componentName: string): PoolStats {
    if (!this.stats.has(componentName)) {
      this.stats.set(componentName, {
        totalCreated: 0,
        totalReused: 0,
        currentPoolSize: 0,
        maxPoolSize: this.config.maxSize,
        hitRate: 0,
        memoryUsage: 0,
      })
    }
    return this.stats.get(componentName)!
  }

  private getActiveInstances(componentName: string): Set<ComponentInstance> {
    if (!this.activeInstances.has(componentName)) {
      this.activeInstances.set(componentName, new Set())
    }
    return this.activeInstances.get(componentName)!
  }

  private createInstance(component: Component): ComponentInstance {
    return {
      component: markRaw(component),
      createdAt: Date.now(),
      lastUsed: Date.now(),
      useCount: 0,
      isActive: false,
    }
  }

  private updateHitRate(componentName: string): void {
    const stats = this.getOrCreateStats(componentName)
    const total = stats.totalCreated + stats.totalReused
    stats.hitRate = total > 0 ? (stats.totalReused / total) * 100 : 0
    
    const pool = this.pools.get(componentName)
    if (pool) {
      stats.currentPoolSize = pool.length
      stats.memoryUsage = pool.length * 1024 // 估算值
    }
  }

  private startAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }
}

// 全局高级组件池实例
export const globalAdvancedComponentPool = new AdvancedComponentPool()

// 在开发环境暴露调试接口
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__ADVANCED_COMPONENT_POOL__ = {
    pool: globalAdvancedComponentPool,
    stats: () => console.table(globalAdvancedComponentPool.getStats()),
    cleanup: () => globalAdvancedComponentPool.cleanup(),
    memory: () => console.log(`内存使用: ${globalAdvancedComponentPool.getMemoryUsage()} bytes`),
  }
}
