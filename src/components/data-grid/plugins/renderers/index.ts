/**
 * 数据网格渲染器总索引 - 懒加载优化版
 */

import { defineAsyncComponent } from 'vue'

// 导出类型定义（类型导入不影响bundle）
export * from '../types'

// 懒加载渲染器组件
export const ActionsRenderer = defineAsyncComponent(() => import('./actions/renderer.vue'))
export const BooleanRenderer = defineAsyncComponent(() => import('./boolean/renderer.vue'))
export const LinkRenderer = defineAsyncComponent(() => import('./link/renderer.vue'))
export const StatusRenderer = defineAsyncComponent(() => import('./status/renderer.vue'))
export const CompositeRenderer = defineAsyncComponent(() => import('./composite/renderer.vue'))
export const DateRenderer = defineAsyncComponent(() => import('./date/renderer.vue'))
export const NumberRenderer = defineAsyncComponent(() => import('./number/renderer.vue'))
export const RelationRenderer = defineAsyncComponent(() => import('./relation/renderer.vue'))
export const UserRenderer = defineAsyncComponent(() => import('./user/renderer.vue'))

// 懒加载工具函数（按需导入）
export const useActionsRenderer = () => import('./actions/useRenderer').then(m => m.useActionsRenderer)
export const useBooleanRenderer = () => import('./boolean/useRenderer').then(m => m.useBooleanRenderer)
export const useLinkRenderer = () => import('./link/useRenderer').then(m => m.useLinkRenderer)
export const useStatusRenderer = () => import('./status/useRenderer').then(m => m.useStatusRenderer)
export const useCompositeRenderer = () => import('./composite/useRenderer').then(m => m.useCompositeRenderer)
export const useDateRenderer = () => import('./date/useRenderer').then(m => m.useDateRenderer)
export const useNumberRenderer = () => import('./number/useRenderer').then(m => m.useNumberRenderer)
export const useRelationRenderer = () => import('./relation/useRenderer').then(m => m.useRelationRenderer)
export const useUserRenderer = () => import('./user/useRenderer').then(m => m.useUserRenderer)
