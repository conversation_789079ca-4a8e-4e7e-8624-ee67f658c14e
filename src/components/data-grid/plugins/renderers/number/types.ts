import type { BaseRendererConfig, BaseRendererProps } from '../../types'

/**
 * 数值类型枚举
 */
export type NumberType =
  | 'integer' // 整数
  | 'decimal' // 小数
  | 'currency' // 货币
  | 'percentage' // 百分比
  | 'ratio' // 比率
  | 'scientific' // 科学计数法

/**
 * 数值显示变体
 */
export type NumberVariant =
  | 'text' // 纯文本
  | 'badge' // 徽章样式
  | 'colored' // 正负数不同颜色
  | 'progress' // 进度条样式（用于百分比）

/**
 * 货币配置接口
 */
export interface CurrencyConfig {
  /** 货币代码 */
  code: string
  /** 汇率 */
  exchange_rate: string
  /** 货币符号 */
  symbol: string
}

/**
 * 数值格式配置
 */
export interface NumberFormatConfig {
  /** 小数位数，默认根据类型自动设置 */
  decimalPlaces?: number
  /** 最小小数位数 */
  minimumFractionDigits?: number
  /** 最大小数位数 */
  maximumFractionDigits?: number
  /** 是否使用千分位分隔符 */
  useGrouping?: boolean
  /** 自定义千分位分隔符 */
  groupingSeparator?: string
  /** 自定义小数点符号 */
  decimalSeparator?: string
}

/**
 * 颜色配置
 */
export interface ColorConfig {
  /** 正数颜色 */
  positiveColor?: string
  /** 负数颜色 */
  negativeColor?: string
  /** 零值颜色 */
  zeroColor?: string
  /** 默认颜色 */
  defaultColor?: string
}

/**
 * 进度条配置
 */
export interface ProgressConfig {
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 进度条颜色 */
  color?: string
  /** 是否显示百分比文本 */
  showText?: boolean
  /** 进度条高度 */
  height?: string
}

/**
 * 数值渲染器配置接口
 */
export interface NumberRendererConfig extends BaseRendererConfig {
  /** 数值类型 */
  numberType?: NumberType
  /** 显示变体 */
  variant?: NumberVariant
  /** 数值格式配置 */
  format?: NumberFormatConfig
  /** 货币配置（当type为currency时使用） */
  currency?: CurrencyConfig
  /** 基础货币代码（用于货币换算） */
  baseCurrency?: string
  /** 颜色配置 */
  colors?: ColorConfig
  /** 进度条配置（当variant为progress时使用） */
  progress?: ProgressConfig
  /** 空值显示文本 */
  nullText?: string
  /** 前缀文本 */
  prefix?: string
  /** 后缀文本 */
  suffix?: string
  /** 是否显示双行货币（原金额+换算金额） */
  showDualCurrency?: boolean
  /** 双行货币显示模式 */
  dualCurrencyMode?: 'primary-secondary' | 'secondary-primary'
}

/**
 * 数值渲染器属性接口
 */
export interface NumberRendererProps extends BaseRendererProps {
  config?: NumberRendererConfig
}

/**
 * 格式化后的数值数据
 */
export interface FormattedNumberData {
  /** 原始值 */
  rawValue: number | null
  /** 格式化后的主显示值 */
  primaryDisplay: string
  /** 次要显示值（用于双货币显示） */
  secondaryDisplay?: string
  /** 是否为负数 */
  isNegative: boolean
  /** 是否为零 */
  isZero: boolean
  /** 是否为空值 */
  isNull: boolean
  /** 进度值（0-100，用于进度条） */
  progressValue?: number
}

/**
 * 区域配置接口
 */
export interface LocaleConfig {
  /** 语言代码 */
  locale: string
  /** 货币代码 */
  currency: string
  /** 数字格式选项 */
  numberFormat: Intl.NumberFormatOptions
}
