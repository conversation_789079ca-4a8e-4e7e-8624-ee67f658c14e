/**
 * 优化版插件管理器 - 性能优先
 *
 * 主要优化：
 * 1. 配置对象复用和缓存
 * 2. 避免重复序列化/反序列化
 * 3. 智能配置清理机制
 * 4. 渲染器实例池
 */

import { markRaw, type Component, shallowRef } from 'vue'
import type { BaseRendererConfig, PluginColumnConfig } from './types'
import { AdvancedComponentPool } from './AdvancedComponentPool'

// 优化的配置缓存系统
class OptimizedConfigCache {
  private configCache = new Map<string, any>()
  private configHash = new Map<any, string>() // 反向索引
  private usageCount = new Map<string, number>()
  private lastAccess = new Map<string, number>()

  // 智能配置 ID 生成 - 优化版本，避免 JSON.stringify
  private generateConfigHash(config: any): string {
    // 直接拼接关键字段，避免 JSON.stringify 的开销
    const parts = [
      config.type || '',
      config.variant || '',
      config.size || '',
      config.color || '',
      String(config.disabled || ''),
      String(config.readonly || ''),
      config.field || '',
      config.title || '',
    ]

    return `config_${this.fastHashCode(parts.join('|'))}`
  }

  private fastHashCode(str: string): string {
    let hash = 2166136261 // FNV offset basis
    for (let i = 0; i < str.length; i++) {
      hash ^= str.charCodeAt(i)
      hash = (hash * 16777619) >>> 0 // FNV prime
    }
    return hash.toString(36)
  }

  // 获取或创建配置 ID（复用相同配置）
  getOrCreateConfigId(config: any): string {
    // 首先检查是否已有相同配置
    if (this.configHash.has(config)) {
      const existingId = this.configHash.get(config)!
      this.recordAccess(existingId)
      return existingId
    }

    const configId = this.generateConfigHash(config)

    // 如果哈希冲突，使用原有配置
    if (this.configCache.has(configId)) {
      this.recordAccess(configId)
      return configId
    }

    // 存储新配置
    this.configCache.set(configId, config)
    this.configHash.set(config, configId)
    this.recordAccess(configId)

    return configId
  }

  private recordAccess(configId: string): void {
    const currentCount = this.usageCount.get(configId) || 0
    this.usageCount.set(configId, currentCount + 1)
    this.lastAccess.set(configId, Date.now())
  }

  get(configId: string): any {
    this.recordAccess(configId)
    return this.configCache.get(configId)
  }

  // 智能清理：移除长时间未使用的配置
  cleanup(maxAge: number = 5 * 60 * 1000): void {
    const now = Date.now()
    const toDelete: string[] = []

    this.lastAccess.forEach((lastTime, configId) => {
      if (now - lastTime > maxAge && (this.usageCount.get(configId) || 0) < 5) {
        toDelete.push(configId)
      }
    })

    toDelete.forEach((configId) => {
      const config = this.configCache.get(configId)
      this.configCache.delete(configId)
      this.configHash.delete(config)
      this.usageCount.delete(configId)
      this.lastAccess.delete(configId)
    })

    if (toDelete.length > 0) {
      console.log(
        `🧹 [OptimizedConfigCache] 清理了 ${toDelete.length} 个过期配置`
      )
    }
  }

  // 获取缓存统计信息
  getStats() {
    return {
      totalConfigs: this.configCache.size,
      mostUsed: Array.from(this.usageCount.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5),
      cacheHitRate: this.calculateHitRate(),
    }
  }

  private calculateHitRate(): number {
    const totalUsage = Array.from(this.usageCount.values()).reduce(
      (a, b) => a + b,
      0
    )
    const uniqueConfigs = this.configCache.size
    return uniqueConfigs > 0
      ? ((totalUsage - uniqueConfigs) / totalUsage) * 100
      : 0
  }
}

// 使用高级组件实例池（向后兼容的包装器）
class ComponentInstancePool {
  private advancedPool: AdvancedComponentPool

  constructor() {
    this.advancedPool = new AdvancedComponentPool({
      maxSize: 15,
      minSize: 3,
      warmupCount: 5,
      maxIdleTime: 3 * 60 * 1000, // 3分钟
      enableAutoCleanup: true,
    })
  }

  get(componentName: string): Component | null {
    // 向后兼容：如果没有组件工厂，返回 null
    return null
  }

  acquire(componentName: string, componentFactory: () => Component): Component {
    return this.advancedPool.acquire(componentName, componentFactory)
  }

  release(componentName: string, instance: Component): void {
    this.advancedPool.release(componentName, instance)
  }

  warmup(componentName: string, component: Component, count: number = 5): void {
    this.advancedPool.warmup(componentName, () => component)
  }

  cleanup(): void {
    this.advancedPool.destroy()
  }

  getStats() {
    return this.advancedPool.getStats()
  }

  getMemoryUsage(): number {
    return this.advancedPool.getMemoryUsage()
  }
}

/**
 * 优化版插件管理器
 */
class OptimizedPluginManager {
  private components = new Map<string, Component>()
  private renderers = new Map<string, any>()
  private configCache = new OptimizedConfigCache()
  private instancePool = new ComponentInstancePool()
  private columnHelper: OptimizedPluginHelper

  // 渲染缓存 - 缓存渲染结果避免重复计算
  private renderCache = new Map<string, string>()
  private renderCacheExpiry = new Map<string, number>()

  constructor() {
    this.columnHelper = new OptimizedPluginHelper(this)

    // 定期清理缓存
    setInterval(
      () => {
        this.configCache.cleanup()
        this.cleanupRenderCache()
      },
      2 * 60 * 1000
    ) // 2分钟清理一次
  }

  registerRenderer(renderer: any): void {
    this.renderers.set(renderer.name, renderer)
    this.registerComponent(renderer.name, renderer.component)

    // 预热组件实例池
    this.instancePool.warmup(renderer.name, renderer.component)
  }

  registerComponent(name: string, component: Component): void {
    this.components.set(name, markRaw(component))
  }

  getComponent(name: string): Component | undefined {
    return this.components.get(name)
  }

  getColumnHelper(): OptimizedPluginHelper {
    return this.columnHelper
  }

  // 优化的配置获取
  getConfig(configId: string): any {
    return this.configCache.get(configId)
  }

  // 渲染结果缓存
  getCachedRender(key: string): string | null {
    const expiry = this.renderCacheExpiry.get(key)
    if (expiry && Date.now() > expiry) {
      this.renderCache.delete(key)
      this.renderCacheExpiry.delete(key)
      return null
    }
    return this.renderCache.get(key) || null
  }

  setCachedRender(key: string, result: string, ttl: number = 30000): void {
    this.renderCache.set(key, result)
    this.renderCacheExpiry.set(key, Date.now() + ttl)
  }

  private cleanupRenderCache(): void {
    const now = Date.now()
    const toDelete: string[] = []

    this.renderCacheExpiry.forEach((expiry, key) => {
      if (now > expiry) {
        toDelete.push(key)
      }
    })

    toDelete.forEach((key) => {
      this.renderCache.delete(key)
      this.renderCacheExpiry.delete(key)
    })
  }

  // 获取性能统计信息
  getPerformanceStats() {
    return {
      config: this.configCache.getStats(),
      renderCache: {
        size: this.renderCache.size,
        hitRate: this.calculateRenderCacheHitRate(),
      },
      componentPool: {
        stats: this.instancePool.getStats(),
        memoryUsage: this.instancePool.getMemoryUsage(),
      },
      components: this.components.size,
    }
  }

  private calculateRenderCacheHitRate(): number {
    // 这里可以添加更详细的缓存命中率统计
    return 0
  }

  cleanup(): void {
    this.configCache.cleanup(0) // 强制清理所有配置
    this.instancePool.cleanup()
    this.renderCache.clear()
    this.renderCacheExpiry.clear()
    this.components.clear()
    this.renderers.clear()
  }

  // 内部方法：创建优化的配置 ID
  _createOptimizedConfigId(config: any): string {
    return this.configCache.getOrCreateConfigId(config)
  }
}

/**
 * 优化版列助手
 */
class OptimizedPluginHelper {
  constructor(private manager: OptimizedPluginManager) {}

  createColumn(
    field: string,
    title: string,
    plugin?: string,
    config?: BaseRendererConfig
  ): PluginColumnConfig {
    const column: PluginColumnConfig = {
      field,
      title,
    }

    if (plugin) {
      const renderer = (this.manager as any).renderers.get(plugin)
      column.plugin = plugin
      column.pluginConfig = {
        ...renderer?.defaultConfig,
        ...config,
      }

      if (renderer?.defaultWidth) {
        column.width = config?.width || renderer.defaultWidth
      }

      // 优化的渲染函数 - 减少重复计算
      column.slots = {
        default: {
          render: (params: any) => {
            // 创建缓存键 - 优化版本，避免 JSON.stringify
            const valueKey = this.createValueKey(params.value)
            const cacheKey = `${plugin}_${field}_${valueKey}`

            // 尝试从缓存获取
            const cached = this.manager.getCachedRender(cacheKey)
            if (cached) {
              return cached
            }

            // 使用优化的配置 ID 生成
            const configId = this.manager._createOptimizedConfigId(
              column.pluginConfig
            )

            const componentProps = {
              value: params.value,
              row: params.row,
              field: field,
              configId: configId,
              column: params.column || {},
            }

            const result = `__COMPONENT__:${plugin}:${this.createPropsKey(componentProps)}`

            // 缓存结果（对于静态内容）
            if (this.isStaticContent(params.value)) {
              this.manager.setCachedRender(cacheKey, result)
            }

            return result
          },
        },
      }
    }

    if (config) {
      Object.keys(config).forEach((key) => {
        if (key !== 'width' && !column.hasOwnProperty(key)) {
          column[key] = config[key]
        }
      })
    }

    return column
  }

  private isStaticContent(value: any): boolean {
    // 判断内容是否为静态（不经常变化）
    return (
      typeof value === 'string' ||
      typeof value === 'number' ||
      typeof value === 'boolean'
    )
  }

  /**
   * 创建值的缓存键，避免 JSON.stringify
   */
  private createValueKey(value: any): string {
    if (value === null || value === undefined) return 'null'
    if (typeof value === 'string') return `s:${value}`
    if (typeof value === 'number') return `n:${value}`
    if (typeof value === 'boolean') return `b:${value}`
    if (Array.isArray(value)) return `a:${value.length}`
    if (typeof value === 'object') return `o:${Object.keys(value).length}`
    return String(value)
  }

  /**
   * 创建组件属性的缓存键，避免 JSON.stringify
   */
  private createPropsKey(props: any): string {
    const parts = [
      this.createValueKey(props.value),
      props.field || '',
      props.configId || '',
      String(props.row?.id || ''),
    ]
    return parts.join('|')
  }

  // 继承原有的列创建方法
  status(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'StatusRenderer', {
      variant: 'badge',
      autoFromMetadata: config?.statusMap ? false : true,
      ...config,
    })
  }

  boolean(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'BooleanRenderer', {
      variant: 'badge',
      trueText: '是',
      falseText: '否',
      ...config,
    })
  }

  mail(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showTypeIcon: true,
      showExternal: true,
      truncate: true,
      linkType: 'mail',
      ...config,
    })
  }

  phone(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showExternal: false,
      showTypeIcon: true,
      truncate: false,
      linkType: 'phone',
      ...config,
    })
  }

  actions(title: string, config?: any): PluginColumnConfig {
    return this.createColumn('actions', title, 'ActionsRenderer', {
      layout: 'horizontal',
      actions: [],
      ...config,
    })
  }

  composite(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'CompositeRenderer', {
      main: { field: field },
      ...config,
    })
  }

  relativeTime(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      variant: 'relative',
      ...config,
    })
  }

  smartTime(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      variant: 'smart',
      ...config,
    })
  }
}

// 创建优化版插件管理器的工厂函数
export function createOptimizedPluginManager(): OptimizedPluginManager {
  return new OptimizedPluginManager()
}
