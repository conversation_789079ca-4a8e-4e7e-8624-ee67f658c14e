/**
 * 优化版插件管理器 - 性能优先
 *
 * 主要优化：
 * 1. 配置对象复用和缓存
 * 2. 避免重复序列化/反序列化
 * 3. 智能配置清理机制
 * 4. 渲染器实例池
 */

import { markRaw, type Component, shallowRef } from 'vue'
import type { BaseRendererConfig, PluginColumnConfig } from './types'

// 优化的配置缓存系统
class OptimizedConfigCache {
  private configCache = new Map<string, any>()
  private configHash = new Map<any, string>() // 反向索引
  private usageCount = new Map<string, number>()
  private lastAccess = new Map<string, number>()

  // 智能配置 ID 生成 - 基于内容哈希而非时间戳
  private generateConfigHash(config: any): string {
    const configStr = JSON.stringify(config, Object.keys(config).sort())
    return `config_${this.hashCode(configStr)}`
  }

  private hashCode(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  // 获取或创建配置 ID（复用相同配置）
  getOrCreateConfigId(config: any): string {
    // 首先检查是否已有相同配置
    if (this.configHash.has(config)) {
      const existingId = this.configHash.get(config)!
      this.recordAccess(existingId)
      return existingId
    }

    const configId = this.generateConfigHash(config)

    // 如果哈希冲突，使用原有配置
    if (this.configCache.has(configId)) {
      this.recordAccess(configId)
      return configId
    }

    // 存储新配置
    this.configCache.set(configId, config)
    this.configHash.set(config, configId)
    this.recordAccess(configId)

    return configId
  }

  private recordAccess(configId: string): void {
    const currentCount = this.usageCount.get(configId) || 0
    this.usageCount.set(configId, currentCount + 1)
    this.lastAccess.set(configId, Date.now())
  }

  get(configId: string): any {
    this.recordAccess(configId)
    return this.configCache.get(configId)
  }

  // 智能清理：移除长时间未使用的配置
  cleanup(maxAge: number = 5 * 60 * 1000): void {
    const now = Date.now()
    const toDelete: string[] = []

    this.lastAccess.forEach((lastTime, configId) => {
      if (now - lastTime > maxAge && (this.usageCount.get(configId) || 0) < 5) {
        toDelete.push(configId)
      }
    })

    toDelete.forEach((configId) => {
      const config = this.configCache.get(configId)
      this.configCache.delete(configId)
      this.configHash.delete(config)
      this.usageCount.delete(configId)
      this.lastAccess.delete(configId)
    })

    if (toDelete.length > 0) {
      console.log(
        `🧹 [OptimizedConfigCache] 清理了 ${toDelete.length} 个过期配置`
      )
    }
  }

  // 获取缓存统计信息
  getStats() {
    return {
      totalConfigs: this.configCache.size,
      mostUsed: Array.from(this.usageCount.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5),
      cacheHitRate: this.calculateHitRate(),
    }
  }

  private calculateHitRate(): number {
    const totalUsage = Array.from(this.usageCount.values()).reduce(
      (a, b) => a + b,
      0
    )
    const uniqueConfigs = this.configCache.size
    return uniqueConfigs > 0
      ? ((totalUsage - uniqueConfigs) / totalUsage) * 100
      : 0
  }
}

// 优化的组件实例池
class ComponentInstancePool {
  private pools = new Map<string, Component[]>()
  private inUse = new Set<Component>()

  get(componentName: string): Component | null {
    const pool = this.pools.get(componentName)
    if (pool && pool.length > 0) {
      const instance = pool.pop()!
      this.inUse.add(instance)
      return instance
    }
    return null
  }

  release(componentName: string, instance: Component): void {
    if (this.inUse.has(instance)) {
      this.inUse.delete(instance)

      if (!this.pools.has(componentName)) {
        this.pools.set(componentName, [])
      }

      const pool = this.pools.get(componentName)!
      if (pool.length < 10) {
        // 限制池大小
        pool.push(instance)
      }
    }
  }

  warmup(componentName: string, component: Component, count: number = 5): void {
    if (!this.pools.has(componentName)) {
      this.pools.set(componentName, [])
    }

    const pool = this.pools.get(componentName)!
    for (let i = 0; i < count; i++) {
      pool.push(markRaw(component))
    }
  }

  cleanup(): void {
    this.pools.clear()
    this.inUse.clear()
  }
}

/**
 * 优化版插件管理器
 */
class OptimizedPluginManager {
  private components = new Map<string, Component>()
  private renderers = new Map<string, any>()
  private configCache = new OptimizedConfigCache()
  private instancePool = new ComponentInstancePool()
  private columnHelper: OptimizedPluginHelper

  // 渲染缓存 - 缓存渲染结果避免重复计算
  private renderCache = new Map<string, string>()
  private renderCacheExpiry = new Map<string, number>()

  constructor() {
    this.columnHelper = new OptimizedPluginHelper(this)

    // 定期清理缓存
    setInterval(
      () => {
        this.configCache.cleanup()
        this.cleanupRenderCache()
      },
      2 * 60 * 1000
    ) // 2分钟清理一次
  }

  registerRenderer(renderer: any): void {
    this.renderers.set(renderer.name, renderer)
    this.registerComponent(renderer.name, renderer.component)

    // 预热组件实例池
    this.instancePool.warmup(renderer.name, renderer.component)
  }

  registerComponent(name: string, component: Component): void {
    this.components.set(name, markRaw(component))
  }

  getComponent(name: string): Component | undefined {
    return this.components.get(name)
  }

  getColumnHelper(): OptimizedPluginHelper {
    return this.columnHelper
  }

  // 优化的配置获取
  getConfig(configId: string): any {
    return this.configCache.get(configId)
  }

  // 渲染结果缓存
  getCachedRender(key: string): string | null {
    const expiry = this.renderCacheExpiry.get(key)
    if (expiry && Date.now() > expiry) {
      this.renderCache.delete(key)
      this.renderCacheExpiry.delete(key)
      return null
    }
    return this.renderCache.get(key) || null
  }

  setCachedRender(key: string, result: string, ttl: number = 30000): void {
    this.renderCache.set(key, result)
    this.renderCacheExpiry.set(key, Date.now() + ttl)
  }

  private cleanupRenderCache(): void {
    const now = Date.now()
    const toDelete: string[] = []

    this.renderCacheExpiry.forEach((expiry, key) => {
      if (now > expiry) {
        toDelete.push(key)
      }
    })

    toDelete.forEach((key) => {
      this.renderCache.delete(key)
      this.renderCacheExpiry.delete(key)
    })
  }

  // 获取性能统计信息
  getPerformanceStats() {
    return {
      config: this.configCache.getStats(),
      renderCache: {
        size: this.renderCache.size,
        hitRate: this.calculateRenderCacheHitRate(),
      },
      components: this.components.size,
    }
  }

  private calculateRenderCacheHitRate(): number {
    // 这里可以添加更详细的缓存命中率统计
    return 0
  }

  cleanup(): void {
    this.configCache.cleanup(0) // 强制清理所有配置
    this.instancePool.cleanup()
    this.renderCache.clear()
    this.renderCacheExpiry.clear()
    this.components.clear()
    this.renderers.clear()
  }

  // 内部方法：创建优化的配置 ID
  _createOptimizedConfigId(config: any): string {
    return this.configCache.getOrCreateConfigId(config)
  }
}

/**
 * 优化版列助手
 */
class OptimizedPluginHelper {
  constructor(private manager: OptimizedPluginManager) {}

  createColumn(
    field: string,
    title: string,
    plugin?: string,
    config?: BaseRendererConfig
  ): PluginColumnConfig {
    const column: PluginColumnConfig = {
      field,
      title,
    }

    if (plugin) {
      const renderer = (this.manager as any).renderers.get(plugin)
      column.plugin = plugin
      column.pluginConfig = {
        ...renderer?.defaultConfig,
        ...config,
      }

      if (renderer?.defaultWidth) {
        column.width = config?.width || renderer.defaultWidth
      }

      // 优化的渲染函数 - 减少重复计算
      column.slots = {
        default: {
          render: (params: any) => {
            // 创建缓存键
            const cacheKey = `${plugin}_${field}_${JSON.stringify(params.value)}`

            // 尝试从缓存获取
            const cached = this.manager.getCachedRender(cacheKey)
            if (cached) {
              return cached
            }

            // 使用优化的配置 ID 生成
            const configId = this.manager._createOptimizedConfigId(
              column.pluginConfig
            )

            const componentProps = {
              value: params.value,
              row: params.row,
              field: field,
              configId: configId,
              column: params.column || {},
            }

            const result = `__COMPONENT__:${plugin}:${JSON.stringify(componentProps)}`

            // 缓存结果（对于静态内容）
            if (this.isStaticContent(params.value)) {
              this.manager.setCachedRender(cacheKey, result)
            }

            return result
          },
        },
      }
    }

    if (config) {
      Object.keys(config).forEach((key) => {
        if (key !== 'width' && !column.hasOwnProperty(key)) {
          column[key] = config[key]
        }
      })
    }

    return column
  }

  private isStaticContent(value: any): boolean {
    // 判断内容是否为静态（不经常变化）
    return (
      typeof value === 'string' ||
      typeof value === 'number' ||
      typeof value === 'boolean'
    )
  }

  // 继承原有的列创建方法
  status(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'StatusRenderer', {
      variant: 'badge',
      autoFromMetadata: config?.statusMap ? false : true,
      ...config,
    })
  }

  boolean(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'BooleanRenderer', {
      variant: 'badge',
      trueText: '是',
      falseText: '否',
      ...config,
    })
  }

  mail(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showTypeIcon: true,
      showExternal: true,
      truncate: true,
      linkType: 'mail',
      ...config,
    })
  }

  phone(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showExternal: false,
      showTypeIcon: true,
      truncate: false,
      linkType: 'phone',
      ...config,
    })
  }

  actions(title: string, config?: any): PluginColumnConfig {
    return this.createColumn('actions', title, 'ActionsRenderer', {
      layout: 'horizontal',
      actions: [],
      ...config,
    })
  }

  composite(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'CompositeRenderer', {
      main: { field: field },
      ...config,
    })
  }

  relativeTime(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      variant: 'relative',
      ...config,
    })
  }

  smartTime(field: string, title: string, config?: any): PluginColumnConfig {
    return this.createColumn(field, title, 'DateRenderer', {
      variant: 'smart',
      ...config,
    })
  }
}

// 创建优化版插件管理器的工厂函数
export function createOptimizedPluginManager(): OptimizedPluginManager {
  return new OptimizedPluginManager()
}
