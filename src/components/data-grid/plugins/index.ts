/**
 * 简化的插件系统入口
 * 替代复杂的插件系统，提供基本的渲染器功能
 */

import { PluginManager, type PluginRenderer } from './manager'

// 导入新的模块化渲染器组件
import { StatusRenderer } from './renderers/status'
import { <PERSON>oleanRenderer } from './renderers/boolean'
import { LinkRenderer } from './renderers/link'
import { ActionsRenderer } from './renderers/actions'
import { CompositeRenderer } from './renderers/composite'
import { DateRenderer } from './renderers/date'
import { NumberRenderer } from './renderers/number'
import { RelationRenderer } from './renderers/relation'
import { UserRenderer } from './renderers/user'

// 定义核心渲染器
const coreRenderers: PluginRenderer[] = [
  {
    name: 'StatusRenderer',
    component: StatusRenderer,
    defaultWidth: 120,
  },
  {
    name: 'BooleanRenderer',
    component: BooleanRenderer,
    defaultWidth: 80,
  },
  {
    name: '<PERSON><PERSON>ender<PERSON>',
    component: <PERSON><PERSON>enderer,
    defaultWidth: 150,
  },
  {
    name: 'ActionsRenderer',
    component: ActionsRenderer,
    defaultWidth: 120,
  },
  {
    name: 'CompositeRenderer',
    component: CompositeRenderer,
    defaultWidth: 200,
  },
  {
    name: 'DateRenderer',
    component: DateRenderer,
    defaultWidth: 150,
    defaultConfig: {
      format: 'yyyy-MM-dd',
      variant: 'text',
    },
  },
  {
    name: 'NumberRenderer',
    component: NumberRenderer,
    defaultWidth: 120,
    defaultConfig: {
      type: 'decimal',
      variant: 'text',
      nullText: '--',
    },
  },
  {
    name: 'RelationRenderer',
    component: RelationRenderer,
    defaultWidth: 150,
    defaultConfig: {
      variant: 'text',
      autoFromMetadata: true,
      overflowMode: 'tooltip',
      maxDisplay: 2,
    },
  },
  {
    name: 'UserRenderer',
    component: UserRenderer,
    defaultWidth: 120,
    defaultConfig: {
      variant: 'text',
      displayFormat: 'username',
      loadingText: '加载中...',
      errorText: '加载失败',
      nullText: '--',
      enableCache: true,
      showTooltip: false,
    },
  },
]

// 全局插件管理器实例
let globalManager: PluginManager | null = null

/**
 * 创建简化的插件管理器
 */
export function createPluginManager(): PluginManager {
  const manager = new PluginManager()

  // 注册核心渲染器
  coreRenderers.forEach((renderer) => {
    manager.registerRenderer(renderer)
  })

  return manager
}

/**
 * 获取全局插件管理器
 */
export function getPluginManager(): PluginManager {
  if (!globalManager) {
    globalManager = createPluginManager()
  }
  return globalManager
}

// 导出类型和接口
export type { PluginRenderer } from './manager'
export { PluginManager, PluginHelper } from './manager'
