/**
 * DataGrid 性能优化配置
 */

export interface PerformanceConfig {
  // 数据相关配置
  data: {
    /** 分页大小建议（大于此值会显示性能警告） */
    recommendedPageSize: number
    /** 最大分页大小 */
    maxPageSize: number
    /** 启用虚拟滚动的最小行数 */
    virtualScrollThreshold: number
  }

  // 渲染相关配置
  render: {
    /** 启用列缓存 */
    enableColumnCache: boolean
    /** 启用组件缓存 */
    enableComponentCache: boolean
    /** 防抖延迟（毫秒） */
    debounceDelay: number
  }

  // 监控相关配置
  monitoring: {
    /** 启用性能监控 */
    enabled: boolean
    /** 监控采样间隔（毫秒） */
    sampleInterval: number
    /** 性能警告阈值（毫秒） */
    warningThreshold: number
    /** 是否在控制台输出性能报告 */
    logToConsole: boolean
  }

  // 内存相关配置
  memory: {
    /** 缓存最大条目数 */
    maxCacheEntries: number
    /** 缓存过期时间（毫秒） */
    cacheExpiration: number
    /** 启用自动清理 */
    autoCleanup: boolean
  }
}

export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  data: {
    recommendedPageSize: 50,
    maxPageSize: 200,
    virtualScrollThreshold: 100,
  },
  render: {
    enableColumnCache: true,
    enableComponentCache: true,
    debounceDelay: 300,
  },
  monitoring: {
    enabled: import.meta.env.DEV,
    sampleInterval: 2000, // 从1秒改为2秒，减少开销
    warningThreshold: 1000, // 1秒
    logToConsole: import.meta.env.DEV,
  },
  memory: {
    maxCacheEntries: 50,
    cacheExpiration: 5 * 60 * 1000, // 5分钟
    autoCleanup: true,
  },
}

/**
 * 获取性能配置
 */
export function getPerformanceConfig(): PerformanceConfig {
  return DEFAULT_PERFORMANCE_CONFIG
}

/**
 * 性能检查器
 */
export class PerformanceChecker {
  private config: PerformanceConfig

  constructor(config: PerformanceConfig = DEFAULT_PERFORMANCE_CONFIG) {
    this.config = config
  }

  /**
   * 检查数据大小是否合理
   */
  checkDataSize(
    rowCount: number,
    columnCount: number
  ): {
    isOptimal: boolean
    warnings: string[]
    suggestions: string[]
  } {
    const warnings: string[] = []
    const suggestions: string[] = []

    // 检查行数
    if (rowCount > this.config.data.recommendedPageSize) {
      warnings.push(
        `数据行数 (${rowCount}) 超过建议值 (${this.config.data.recommendedPageSize})`
      )
      suggestions.push('考虑减少分页大小或启用虚拟滚动')
    }

    if (rowCount > this.config.data.maxPageSize) {
      warnings.push(
        `数据行数 (${rowCount}) 超过最大限制 (${this.config.data.maxPageSize})`
      )
      suggestions.push('请减少分页大小以获得更好的性能')
    }

    // 检查列数
    if (columnCount > 20) {
      warnings.push(`列数 (${columnCount}) 较多，可能影响渲染性能`)
      suggestions.push('考虑隐藏部分列或使用列固定功能')
    }

    // 检查总数据量
    const totalCells = rowCount * columnCount
    if (totalCells > 10000) {
      warnings.push(`总数据量 (${totalCells} 个单元格) 较大`)
      suggestions.push('启用虚拟滚动或优化列配置')
    }

    return {
      isOptimal: warnings.length === 0,
      warnings,
      suggestions,
    }
  }

  /**
   * 检查渲染时间
   */
  checkRenderTime(renderTime: number): {
    isOptimal: boolean
    level: 'good' | 'warning' | 'critical'
    message: string
  } {
    if (renderTime < 100) {
      return {
        isOptimal: true,
        level: 'good',
        message: `渲染时间 ${renderTime.toFixed(2)}ms - 优秀`,
      }
    } else if (renderTime < this.config.monitoring.warningThreshold) {
      return {
        isOptimal: true,
        level: 'warning',
        message: `渲染时间 ${renderTime.toFixed(2)}ms - 尚可接受`,
      }
    } else {
      return {
        isOptimal: false,
        level: 'critical',
        message: `渲染时间 ${renderTime.toFixed(2)}ms - 需要优化`,
      }
    }
  }
}

/**
 * 全局性能检查器实例
 */
export const performanceChecker = new PerformanceChecker()
