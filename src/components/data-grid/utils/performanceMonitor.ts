/**
 * Data-Grid 性能监控工具
 * 提供实时性能监控、内存使用跟踪和性能报告功能
 */

interface PerformanceMetrics {
  /** 组件渲染时间（毫秒） */
  renderTime: number
  /** 数据加载时间（毫秒） */
  dataLoadTime: number
  /** 页面总内存使用量（MB） - 注意：这是整个标签页的内存，非单组件 */
  totalMemoryUsage: number
  /** 组件数据量估算（记录数 × 列数） */
  componentDataSize: number
  /** 缓存命中率（百分比） */
  cacheHitRate: number
  /** 重新渲染次数 */
  rerenderCount: number
  /** 最后更新时间 */
  lastUpdate: Date
}

interface PerformanceConfig {
  /** 是否启用性能监控 */
  enabled: boolean
  /** 监控数据保留时间（毫秒） */
  retentionTime: number
  /** 采样频率（毫秒） */
  sampleInterval: number
  /** 是否在控制台输出性能报告 */
  logToConsole: boolean
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor | null = null
  private metrics: Map<string, PerformanceMetrics> = new Map()
  private config: PerformanceConfig
  private observers: Map<string, PerformanceObserver> = new Map()
  private timers: Map<string, number> = new Map()

  private constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enabled: import.meta.env.DEV,
      retentionTime: 5 * 60 * 1000, // 5分钟
      sampleInterval: 1000, // 1秒
      logToConsole: false,
      ...config,
    }

    if (this.config.enabled) {
      this.initializeMonitoring()
    }
  }

  static getInstance(config?: Partial<PerformanceConfig>): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor(config)
    }
    return this.instance
  }

  /**
   * 初始化性能监控
   */
  private initializeMonitoring(): void {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.duration > 50) {
            // 长于50ms的任务
            console.warn(
              `[PerformanceMonitor] 检测到长任务: ${entry.duration.toFixed(2)}ms`
            )
          }
        })
      })

      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.set('longtask', longTaskObserver)
      } catch (error) {
        console.warn('[PerformanceMonitor] 长任务监控不支持')
      }
    }

    // 定期采样内存使用
    const memoryTimer = setInterval(() => {
      this.sampleMemoryUsage()
    }, this.config.sampleInterval)

    this.timers.set('memory', memoryTimer)
  }

  /**
   * 开始监控组件性能
   */
  startComponentMonitoring(componentId: string): void {
    if (!this.config.enabled) return

    const startTime = performance.now()
    performance.mark(`${componentId}-start`)

    // 初始化组件指标
    this.metrics.set(componentId, {
      renderTime: 0,
      dataLoadTime: 0,
      totalMemoryUsage: this.getCurrentMemoryUsage(),
      componentDataSize: 0,
      cacheHitRate: 0,
      rerenderCount: 0,
      lastUpdate: new Date(),
    })

    console.log(`[PerformanceMonitor] 开始监控组件: ${componentId}`)
  }

  /**
   * 记录组件渲染时间
   */
  recordRenderTime(componentId: string): void {
    if (!this.config.enabled) return

    try {
      performance.mark(`${componentId}-end`)
      performance.measure(
        `${componentId}-render`,
        `${componentId}-start`,
        `${componentId}-end`
      )

      const measure = performance.getEntriesByName(`${componentId}-render`)[0]
      const metrics = this.metrics.get(componentId)

      if (metrics && measure) {
        metrics.renderTime = measure.duration
        metrics.rerenderCount++
        metrics.lastUpdate = new Date()

        if (this.config.logToConsole) {
          console.log(
            `[PerformanceMonitor] ${componentId} 渲染时间: ${measure.duration.toFixed(2)}ms`
          )
        }
      }
    } catch (error) {
      console.warn(
        `[PerformanceMonitor] 记录渲染时间失败: ${componentId}`,
        error
      )
    }
  }

  /**
   * 记录数据加载时间
   */
  recordDataLoadTime(componentId: string, loadTime: number): void {
    if (!this.config.enabled) return

    const metrics = this.metrics.get(componentId)
    if (metrics) {
      metrics.dataLoadTime = loadTime
      metrics.lastUpdate = new Date()

      if (this.config.logToConsole) {
        console.log(
          `[PerformanceMonitor] ${componentId} 数据加载时间: ${loadTime.toFixed(2)}ms`
        )
      }
    }
  }

  /**
   * 记录缓存命中率
   */
  recordCacheHitRate(componentId: string, hitRate: number): void {
    if (!this.config.enabled) return

    const metrics = this.metrics.get(componentId)
    if (metrics) {
      metrics.cacheHitRate = hitRate
      metrics.lastUpdate = new Date()
    }
  }

  /**
   * 记录组件数据大小（行数 × 列数）
   */
  recordComponentDataSize(
    componentId: string,
    rows: number,
    cols: number
  ): void {
    if (!this.config.enabled) return

    const metrics = this.metrics.get(componentId)
    if (metrics) {
      metrics.componentDataSize = rows * cols
      metrics.lastUpdate = new Date()

      if (this.config.logToConsole) {
        console.log(
          `[PerformanceMonitor] ${componentId} 数据量: ${rows}行 × ${cols}列 = ${metrics.componentDataSize}个单元格`
        )
      }
    }
  }

  /**
   * 获取组件性能指标
   */
  getMetrics(componentId: string): PerformanceMetrics | null {
    return this.metrics.get(componentId) || null
  }

  /**
   * 获取所有组件的性能指标
   */
  getAllMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.metrics)
  }

  /**
   * 生成性能报告
   */
  generateReport(componentId?: string): string {
    const metrics = componentId
      ? new Map([[componentId, this.metrics.get(componentId)!]])
      : this.metrics

    const report = ['=== Data-Grid 性能报告 ===']

    metrics.forEach((metric, id) => {
      if (metric) {
        report.push(`\n组件: ${id}`)
        report.push(`  渲染时间: ${metric.renderTime.toFixed(2)}ms`)
        report.push(`  数据加载时间: ${metric.dataLoadTime.toFixed(2)}ms`)
        report.push(
          `  页面总内存: ${metric.totalMemoryUsage.toFixed(2)}MB (整个标签页)`
        )
        report.push(`  组件数据量: ${metric.componentDataSize}个单元格`)
        report.push(`  缓存命中率: ${metric.cacheHitRate.toFixed(1)}%`)
        report.push(`  重新渲染次数: ${metric.rerenderCount}`)
        report.push(`  最后更新: ${metric.lastUpdate.toLocaleTimeString()}`)
      }
    })

    return report.join('\n')
  }

  /**
   * 清理过期数据
   */
  cleanup(): void {
    const now = Date.now()
    const toDelete: string[] = []

    this.metrics.forEach((metric, id) => {
      if (now - metric.lastUpdate.getTime() > this.config.retentionTime) {
        toDelete.push(id)
      }
    })

    toDelete.forEach((id) => {
      this.metrics.delete(id)
      performance.clearMarks(`${id}-start`)
      performance.clearMarks(`${id}-end`)
      performance.clearMeasures(`${id}-render`)
    })

    if (toDelete.length > 0) {
      console.log(
        `[PerformanceMonitor] 清理了 ${toDelete.length} 个过期组件的监控数据`
      )
    }
  }

  /**
   * 停止监控
   */
  stopMonitoring(componentId?: string): void {
    if (componentId) {
      this.metrics.delete(componentId)
      performance.clearMarks(`${componentId}-start`)
      performance.clearMarks(`${componentId}-end`)
      performance.clearMeasures(`${componentId}-render`)

      if (import.meta.env.DEV) {
        console.log(`[PerformanceMonitor] 停止监控组件: ${componentId}`)
      }
    } else {
      this.destroy()
    }
  }

  /**
   * 完全销毁监控器实例
   */
  destroy(): void {
    // 停止所有观察者
    this.observers.forEach((observer) => {
      try {
        observer.disconnect()
      } catch (error) {
        console.warn('[PerformanceMonitor] 断开观察者时出错:', error)
      }
    })
    this.observers.clear()

    // 清理所有定时器
    this.timers.forEach((timer) => {
      try {
        clearInterval(timer)
      } catch (error) {
        console.warn('[PerformanceMonitor] 清理定时器时出错:', error)
      }
    })
    this.timers.clear()

    // 清理所有指标数据
    this.metrics.clear()

    // 清理全局实例
    PerformanceMonitor.instance = null

    if (import.meta.env.DEV) {
      console.log('[PerformanceMonitor] 监控器已完全销毁')
    }
  }

  /**
   * 采样内存使用 - 优化版本，减少频繁更新
   */
  private sampleMemoryUsage(): void {
    const memoryUsage = this.getCurrentMemoryUsage()
    const now = Date.now()

    // 只有内存使用变化超过 10MB 或者超过 30 秒才更新
    this.metrics.forEach((metric) => {
      const memoryDiff = Math.abs(metric.totalMemoryUsage - memoryUsage)
      const timeDiff = now - metric.lastUpdate.getTime()

      if (memoryDiff > 10 || timeDiff > 30000) {
        metric.totalMemoryUsage = memoryUsage
        metric.lastUpdate = new Date()
      }
    })
  }

  /**
   * 获取当前内存使用量
   */
  private getCurrentMemoryUsage(): number {
    if ('memory' in performance && (performance as any).memory) {
      const memory = (performance as any).memory
      return memory.usedJSHeapSize / 1024 / 1024 // 转换为 MB
    }
    return 0
  }

  /**
   * 检查性能瓶颈
   */
  checkPerformanceBottlenecks(componentId?: string): string[] {
    const issues: string[] = []
    const metrics = componentId
      ? new Map([[componentId, this.metrics.get(componentId)!]])
      : this.metrics

    metrics.forEach((metric, id) => {
      if (!metric) return

      // 检查渲染时间
      if (metric.renderTime > 100) {
        issues.push(`${id}: 渲染时间过长 (${metric.renderTime.toFixed(2)}ms)`)
      }

      // 检查数据加载时间
      if (metric.dataLoadTime > 2000) {
        issues.push(
          `${id}: 数据加载时间过长 (${metric.dataLoadTime.toFixed(2)}ms)`
        )
      }

      // 检查页面总内存使用（调整阈值和说明）
      if (metric.totalMemoryUsage > 800) {
        issues.push(
          `${id}: 页面总内存使用过高 (${metric.totalMemoryUsage.toFixed(2)}MB) - 考虑刷新页面或关闭其他标签页`
        )
      }

      // 检查组件数据量
      if (metric.componentDataSize > 10000) {
        issues.push(
          `${id}: 组件数据量过大 (${metric.componentDataSize}个单元格) - 建议启用分页或虚拟滚动`
        )
      }

      // 检查重新渲染频率
      if (metric.rerenderCount > 10) {
        issues.push(`${id}: 重新渲染过于频繁 (${metric.rerenderCount}次)`)
      }

      // 检查缓存命中率（增加时间判断，避免初始化时的误报）
      const componentAge = Date.now() - metric.lastUpdate.getTime()
      if (metric.cacheHitRate < 30 && componentAge > 30000) {
        // 30秒后才检查，降低阈值到30%
        issues.push(
          `${id}: 缓存命中率过低 (${metric.cacheHitRate.toFixed(1)}%)`
        )
      }
    })

    return issues
  }

  /**
   * 获取内存优化建议
   */
  getMemoryOptimizationTips(componentId?: string): string[] {
    const tips: string[] = []
    const metrics = componentId
      ? new Map([[componentId, this.metrics.get(componentId)!]])
      : this.metrics

    // 通用内存优化建议
    tips.push('🚀 通用内存优化建议:')
    tips.push('  • 启用虚拟滚动减少DOM节点数量')
    tips.push('  • 合理设置分页大小(建议20-50条)')
    tips.push('  • 定期清理不需要的数据缓存')
    tips.push('  • 避免在组件中存储大量计算结果')

    // 根据当前指标提供具体建议
    const totalMemory = this.getCurrentMemoryUsage()
    if (totalMemory > 500) {
      tips.push('\n⚠️ 当前页面内存使用较高:')
      tips.push('  • 考虑关闭其他不需要的浏览器标签页')
      tips.push('  • 检查是否有内存泄漏的第三方插件')
      tips.push('  • 定期刷新页面释放累积的内存')
    }

    metrics.forEach((metric, id) => {
      if (!metric) return

      if (metric.componentDataSize > 5000) {
        tips.push(`\n📊 ${id} 组件数据量优化:`)
        tips.push('  • 启用服务端分页减少单次加载数据量')
        tips.push('  • 考虑懒加载非关键列数据')
        tips.push('  • 使用列虚拟化处理大量列')
      }

      if (metric.rerenderCount > 5) {
        tips.push(`\n🔄 ${id} 渲染优化:`)
        tips.push('  • 检查是否有不必要的响应式数据变化')
        tips.push('  • 使用 v-memo 或 useMemo 缓存计算结果')
        tips.push('  • 避免在渲染函数中进行复杂计算')
      }

      if (
        metric.cacheHitRate < 50 &&
        Date.now() - metric.lastUpdate.getTime() > 30000
      ) {
        tips.push(`\n💾 ${id} 缓存优化:`)
        tips.push('  • 检查缓存键是否设计合理')
        tips.push('  • 适当增加缓存时间')
        tips.push('  • 预加载常用数据到缓存')
      }
    })

    if (tips.length === 4) {
      tips.push('\n✅ 当前性能表现良好，暂无特别建议')
    }

    return tips
  }

  /**
   * 一键内存分析报告
   */
  getMemoryAnalysisReport(): string {
    const totalMemory = this.getCurrentMemoryUsage()
    const report = ['=== 🧠 内存分析报告 ===\n']

    // 内存概况
    report.push(`📊 页面内存概况:`)
    report.push(`  总内存使用: ${totalMemory.toFixed(2)}MB`)

    if (totalMemory < 200) {
      report.push(`  状态: 🟢 优秀 (< 200MB)`)
    } else if (totalMemory < 400) {
      report.push(`  状态: 🟡 良好 (200-400MB)`)
    } else if (totalMemory < 600) {
      report.push(`  状态: 🟠 注意 (400-600MB)`)
    } else {
      report.push(`  状态: 🔴 警告 (> 600MB)`)
    }

    // 组件分析
    report.push(`\n📈 组件数据分析:`)
    this.metrics.forEach((metric, id) => {
      if (metric) {
        const efficiency =
          metric.componentDataSize > 0
            ? (
                (metric.componentDataSize /
                  Math.max(metric.totalMemoryUsage, 1)) *
                1000
              ).toFixed(1)
            : '0'
        report.push(
          `  ${id}: ${metric.componentDataSize}个单元格, 效率指数: ${efficiency}`
        )
      }
    })

    // 优化建议
    const tips = this.getMemoryOptimizationTips()
    report.push('\n' + tips.join('\n'))

    return report.join('\n')
  }
}

// 创建全局实例
export const performanceMonitor = PerformanceMonitor.getInstance()

// 导出类型
export type { PerformanceMetrics, PerformanceConfig }
export { PerformanceMonitor }

// Vue 插件形式
export const PerformanceMonitorPlugin = {
  install(app: any, options: Partial<PerformanceConfig> = {}) {
    const monitor = PerformanceMonitor.getInstance(options)
    app.config.globalProperties.$performanceMonitor = monitor
    app.provide('performanceMonitor', monitor)
  },
}

// 开发工具集成
if (import.meta.env.DEV && typeof window !== 'undefined') {
  // 暴露到全局对象供开发者使用
  ;(window as any).__DATA_GRID_PERFORMANCE__ = {
    ...performanceMonitor,
    // 便捷方法
    memoryReport: () =>
      console.log(performanceMonitor.getMemoryAnalysisReport()),
    tips: () =>
      console.log(performanceMonitor.getMemoryOptimizationTips().join('\n')),
    help: () => {
      console.log(`
🔧 Data-Grid 性能监控工具使用指南:

📊 基础命令:
  __DATA_GRID_PERFORMANCE__.generateReport()     - 生成性能报告
  __DATA_GRID_PERFORMANCE__.memoryReport()       - 内存分析报告  
  __DATA_GRID_PERFORMANCE__.tips()              - 内存优化建议
  __DATA_GRID_PERFORMANCE__.getAllMetrics()     - 获取所有指标

🧠 内存分析:
  __DATA_GRID_PERFORMANCE__.getMemoryAnalysisReport() - 完整内存报告
  __DATA_GRID_PERFORMANCE__.getCurrentMemoryUsage()   - 当前页面内存
  
🚨 问题检查:
  __DATA_GRID_PERFORMANCE__.checkPerformanceBottlenecks() - 检查性能瓶颈

⚙️ 高级功能:
  __DATA_GRID_PERFORMANCE__.cleanup()           - 清理过期数据
  __DATA_GRID_PERFORMANCE__.stopMonitoring()    - 停止监控

💡 注意: 内存使用量显示的是整个浏览器标签页的内存，不是单个组件的内存
      `)
    },
  }

  // 智能性能监控 - 减少误报和资源消耗
  let lastCheckTime = 0
  const CHECK_INTERVAL = 60000 // 1分钟检查一次，减少频率
  const SEVERITY_THRESHOLD = 2 // 至少2个严重问题才报告

  setInterval(() => {
    const now = Date.now()
    if (now - lastCheckTime < CHECK_INTERVAL) return

    lastCheckTime = now
    const issues = performanceMonitor.checkPerformanceBottlenecks()

    // 只报告严重问题，过滤误报
    const severeIssues = issues.filter((issue) => {
      // 过滤缓存命中率误报（新组件初始化时命中率为0是正常的）
      if (issue.includes('缓存命中率过低') && issue.includes('(0.0%)')) {
        const componentId = issue.split(':')[0]
        const metrics = performanceMonitor.getMetrics(componentId)
        if (metrics && Date.now() - metrics.lastUpdate.getTime() < 10000) {
          return false // 10秒内的新组件忽略缓存命中率问题
        }
      }

      // 过滤页面内存使用误报（600MB以下不算严重问题）
      if (issue.includes('页面总内存使用过高')) {
        const memMatch = issue.match(/(\d+\.\d+)MB/)
        if (memMatch && parseFloat(memMatch[1]) < 600) {
          return false
        }
      }

      return true
    })

    if (severeIssues.length >= SEVERITY_THRESHOLD) {
      console.group('🚨 Data-Grid 严重性能问题')
      severeIssues.forEach((issue) => console.warn(issue))

      // 提供优化建议
      console.group('💡 优化建议')
      console.log('1. 考虑减少同时显示的数据量')
      console.log('2. 启用虚拟滚动以减少DOM节点')
      console.log('3. 检查是否有内存泄漏')
      console.log('4. 考虑清理不需要的缓存')
      console.groupEnd()

      console.groupEnd()
    }
  }, 15000) // 15秒检查间隔，但实际执行频率为1分钟
}

// 页面卸载时清理全局监控器实例，防止内存泄漏
if (typeof window !== 'undefined') {
  const cleanup = () => {
    if (PerformanceMonitor.instance) {
      PerformanceMonitor.instance.destroy()
    }
  }

  // 监听页面卸载事件
  window.addEventListener('beforeunload', cleanup)
  window.addEventListener('pagehide', cleanup)

  // 在开发环境中，也监听热重载
  if (import.meta.env.DEV && import.meta.hot) {
    import.meta.hot.dispose(cleanup)
  }
}
