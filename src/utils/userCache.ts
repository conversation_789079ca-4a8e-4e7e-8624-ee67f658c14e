import { getAllUsersName, type AllUsersName } from '@/api/sys/user'

/**
 * 用户缓存工具类
 * 提供用户ID转名称的功能，包含缓存机制和过期策略
 */
class UserCache {
  private cache: Map<number, AllUsersName> = new Map()
  private cacheExpiry: Map<number, number> = new Map()
  private isLoading: boolean = false
  private loadPromise: Promise<void> | null = null

  // 缓存过期时间：30分钟
  private readonly CACHE_EXPIRY_TIME = 30 * 60 * 1000

  // 强制刷新时间：5分钟
  private readonly FORCE_REFRESH_TIME = 5 * 60 * 1000

  // 上次加载时间
  private lastLoadTime: number = 0

  /**
   * 根据用户ID获取用户名
   * @param userId 用户ID
   * @returns 用户名，如果未找到返回null
   */
  async getUserName(userId: number | string): Promise<string | null> {
    if (!userId) return null

    const id = Number(userId)

    // 检查缓存是否存在且未过期
    if (this.isCacheValid(id)) {
      const user = this.cache.get(id)
      return user?.username || null
    }

    // 如果缓存过期或不存在，尝试加载数据
    await this.ensureDataLoaded()

    // 再次检查缓存
    if (this.cache.has(id)) {
      const user = this.cache.get(id)
      return user?.username || null
    }

    return null
  }

  /**
   * 根据用户ID获取用户完整信息
   * @param userId 用户ID
   * @returns 用户信息，如果未找到返回null
   */
  async getUserInfo(userId: number | string): Promise<AllUsersName | null> {
    if (!userId) return null

    const id = Number(userId)

    // 检查缓存是否存在且未过期
    if (this.isCacheValid(id)) {
      return this.cache.get(id) || null
    }

    // 如果缓存过期或不存在，尝试加载数据
    await this.ensureDataLoaded()

    return this.cache.get(id) || null
  }

  /**
   * 获取所有用户信息
   * @param forceRefresh 是否强制刷新缓存
   * @returns 所有用户信息数组
   */
  async getAllUsers(forceRefresh: boolean = false): Promise<AllUsersName[]> {
    if (forceRefresh || this.shouldRefreshCache()) {
      await this.loadData(true)
    } else {
      await this.ensureDataLoaded()
    }

    return Array.from(this.cache.values())
  }

  /**
   * 根据用户名查找用户ID
   * @param username 用户名
   * @returns 用户ID，如果未找到返回null
   */
  async getUserIdByUsername(username: string): Promise<number | null> {
    if (!username) return null

    await this.ensureDataLoaded()

    for (const [id, user] of this.cache.entries()) {
      if (user.username === username) {
        return id
      }
    }

    return null
  }

  /**
   * 搜索用户（模糊匹配）
   * @param keyword 搜索关键词
   * @returns 匹配的用户数组
   */
  async searchUsers(keyword: string): Promise<AllUsersName[]> {
    if (!keyword) return []

    await this.ensureDataLoaded()

    const results: AllUsersName[] = []
    const lowerKeyword = keyword.toLowerCase()

    for (const user of this.cache.values()) {
      if (user.username.toLowerCase().includes(lowerKeyword)) {
        results.push(user)
      }
    }

    return results
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheExpiry.clear()
    this.lastLoadTime = 0
  }

  /**
   * 手动刷新缓存
   */
  async refreshCache(): Promise<void> {
    await this.loadData(true)
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    totalUsers: number
    cacheSize: number
    lastLoadTime: number
    isLoading: boolean
  } {
    return {
      totalUsers: this.cache.size,
      cacheSize: this.cache.size,
      lastLoadTime: this.lastLoadTime,
      isLoading: this.isLoading,
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(userId: number): boolean {
    const expiryTime = this.cacheExpiry.get(userId)
    if (!expiryTime) return false

    return Date.now() < expiryTime
  }

  /**
   * 检查是否应该刷新缓存
   */
  private shouldRefreshCache(): boolean {
    return Date.now() - this.lastLoadTime > this.FORCE_REFRESH_TIME
  }

  /**
   * 确保数据已加载
   */
  private async ensureDataLoaded(): Promise<void> {
    if (this.cache.size === 0) {
      await this.loadData()
    }
  }

  /**
   * 加载用户数据
   */
  private async loadData(forceRefresh: boolean = false): Promise<void> {
    // 如果正在加载，等待加载完成
    if (this.isLoading && !forceRefresh) {
      if (this.loadPromise) {
        await this.loadPromise
      }
      return
    }

    // 如果强制刷新，清除现有缓存
    if (forceRefresh) {
      this.cache.clear()
      this.cacheExpiry.clear()
    }

    this.isLoading = true

    try {
      this.loadPromise = this.fetchAndCacheUsers()
      await this.loadPromise
    } catch (error) {
      console.error('加载用户数据失败:', error)
      throw error
    } finally {
      this.isLoading = false
      this.loadPromise = null
    }
  }

  /**
   * 从API获取用户数据并缓存
   */
  private async fetchAndCacheUsers(): Promise<void> {
    try {
      console.log('[UserCache] 开始获取用户数据...')
      const users = await getAllUsersName()
      console.log('[UserCache] 获取到用户数据:', users?.length, '个用户')

      if (!users || !Array.isArray(users)) {
        throw new Error('用户API返回数据格式错误')
      }

      // 清除现有缓存
      this.cache.clear()
      this.cacheExpiry.clear()

      // 设置缓存
      const expiryTime = Date.now() + this.CACHE_EXPIRY_TIME
      for (const user of users) {
        if (user && typeof user.id === 'number') {
          this.cache.set(user.id, user)
          this.cacheExpiry.set(user.id, expiryTime)
        } else {
          console.warn('[UserCache] 无效用户数据:', user)
        }
      }

      this.lastLoadTime = Date.now()

      console.log(`[UserCache] 用户缓存已更新，共缓存 ${this.cache.size} 个用户`)
    } catch (error) {
      console.error('[UserCache] 获取用户数据失败:', error)
      throw error
    }
  }
}

// 创建单例实例
const userCache = new UserCache()

// 导出单例实例和类型
export default userCache
export { UserCache }
export type { AllUsersName }
